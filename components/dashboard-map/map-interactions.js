/* Dashboard Interactive Map - JavaScript Functionality */
/* Add this JavaScript to Webflow Page Settings > Custom Code > Footer Code */

// Interactive Map Class
class DashboardMap {
  constructor() {
    // Try multiple selectors to find booth containers
    this.booths = document.querySelectorAll('.booth-container') ||
                  document.querySelectorAll('[data-booth]') ||
                  document.querySelectorAll('a[href*="#"]');

    // Try multiple selectors to find accordion items
    this.accordions = document.querySelectorAll('.accordion-item') ||
                      document.querySelectorAll('[id*="accordion"]') ||
                      document.querySelectorAll('.w-dropdown');

    this.isInitialized = false;
    this.init();
  }
  
  init() {
    // Wait for DOM to be fully loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
    } else {
      this.setupEventListeners();
    }
  }
  
  setupEventListeners() {
    // Add click handlers for booth containers
    this.booths.forEach(booth => {
      booth.addEventListener('click', (e) => {
        e.preventDefault();

        // Try multiple ways to get booth ID
        let boothId = booth.dataset.booth;

        // If no data-booth, try to extract from href
        if (!boothId && booth.href) {
          const hash = booth.href.split('#')[1];
          if (hash) {
            boothId = hash;
          }
        }

        // If still no ID, try to extract from text content or other attributes
        if (!boothId) {
          const text = booth.textContent.trim().toLowerCase();
          if (text.includes('nba decoded')) boothId = 'nba-decoded';
          else if (text.includes('making the call')) boothId = 'making-the-call';
          else if (text.includes('huddle up')) boothId = 'huddle-up';
          else if (text.includes('trust issues')) boothId = 'trust-issues';
        }

        if (boothId) {
          this.handleBoothClick(boothId);
        }
      });

      // Add hover effects
      booth.addEventListener('mouseenter', () => {
        if (!booth.classList.contains('active') && !booth.classList.contains('completed')) {
          booth.style.transform = 'scale(1.05)';
        }
      });

      booth.addEventListener('mouseleave', () => {
        if (!booth.classList.contains('active') && !booth.classList.contains('completed')) {
          booth.style.transform = 'scale(1)';
        }
      });
    });
    
    // Add click handlers for accordion headers
    this.accordions.forEach(accordion => {
      const header = accordion.querySelector('.accordion-header');
      if (header) {
        header.addEventListener('click', () => {
          this.handleAccordionClick(accordion);
        });
      }
    });
    
    this.isInitialized = true;
    console.log('Dashboard Map initialized successfully');
  }
  
  handleBoothClick(boothId) {
    // Open corresponding accordion
    this.openAccordion(boothId);
    
    // Set booth as active
    this.setActiveBooths([boothId]);
    
    // Scroll accordion into view if needed
    this.scrollToAccordion(boothId);
  }
  
  handleAccordionClick(accordion) {
    const isActive = accordion.classList.contains('active');
    
    // Close all accordions first
    this.accordions.forEach(acc => {
      acc.classList.remove('active');
      this.updateAccordionToggle(acc, false);
    });
    
    // Open clicked accordion if it wasn't active
    if (!isActive) {
      accordion.classList.add('active');
      this.updateAccordionToggle(accordion, true);
      
      // Highlight corresponding booth
      const boothId = accordion.id.replace('accordion-', '');
      this.setActiveBooths([boothId]);
    } else {
      // Clear all booth highlights if closing accordion
      this.setActiveBooths([]);
    }
  }
  
  updateAccordionToggle(accordion, isOpen) {
    const toggle = accordion.querySelector('.accordion-toggle');
    if (toggle) {
      toggle.textContent = isOpen ? '×' : '+';
    }
  }
  
  openAccordion(boothId) {
    // Close all accordions first
    this.closeAllAccordions();

    // Try multiple ways to find the target accordion
    let targetAccordion = document.getElementById(`accordion-${boothId}`) ||
                         document.getElementById(boothId) ||
                         document.querySelector(`[data-accordion="${boothId}"]`);

    // If still not found, try to find by content matching
    if (!targetAccordion) {
      const accordions = document.querySelectorAll('.w-dropdown, .accordion-item, [id*="accordion"]');
      accordions.forEach(acc => {
        const text = acc.textContent.toLowerCase();
        if ((boothId === 'nba-decoded' && text.includes('nba decoded')) ||
            (boothId === 'making-the-call' && text.includes('making the call')) ||
            (boothId === 'huddle-up' && text.includes('huddle up')) ||
            (boothId === 'trust-issues' && text.includes('trust issues'))) {
          targetAccordion = acc;
        }
      });
    }

    if (targetAccordion) {
      this.expandAccordion(targetAccordion);
    }
  }

  closeAllAccordions() {
    // Try different accordion structures
    const accordions = document.querySelectorAll('.w-dropdown, .accordion-item, [id*="accordion"]');
    accordions.forEach(acc => {
      // Close Webflow dropdown
      if (acc.classList.contains('w-dropdown')) {
        acc.classList.remove('w--open');
        const toggle = acc.querySelector('.w-dropdown-toggle');
        if (toggle) {
          toggle.setAttribute('aria-expanded', 'false');
        }
      }

      // Close custom accordion
      acc.classList.remove('active');
      this.updateAccordionToggle(acc, false);
    });
  }

  expandAccordion(accordion) {
    // Handle Webflow dropdown
    if (accordion.classList.contains('w-dropdown')) {
      accordion.classList.add('w--open');
      const toggle = accordion.querySelector('.w-dropdown-toggle');
      if (toggle) {
        toggle.setAttribute('aria-expanded', 'true');
        // Trigger click if needed
        toggle.click();
      }
    }

    // Handle custom accordion
    accordion.classList.add('active');
    this.updateAccordionToggle(accordion, true);
  }
  
  setActiveBooths(boothIds) {
    // Clear all booth states
    this.booths.forEach(booth => {
      booth.classList.remove('active');
      booth.style.transform = 'scale(1)';
    });
    
    // Set active booths
    boothIds.forEach(boothId => {
      const booth = document.querySelector(`[data-booth="${boothId}"]`);
      if (booth && !booth.classList.contains('completed')) {
        booth.classList.add('active');
      }
    });
  }
  
  markBoothCompleted(boothId) {
    const booth = document.querySelector(`[data-booth="${boothId}"]`);
    if (booth) {
      booth.classList.remove('active');
      booth.classList.add('completed');
      booth.style.transform = 'scale(1)';
      
      // Add completion animation
      this.animateCompletion(booth);
    }
  }
  
  animateCompletion(booth) {
    // Add a temporary completion effect
    booth.style.animation = 'none';
    booth.offsetHeight; // Trigger reflow
    booth.style.animation = 'boothCompletedGlow 3s ease-in-out infinite';
  }
  
  scrollToAccordion(boothId) {
    // Try to find the accordion using multiple methods
    let accordion = document.getElementById(`accordion-${boothId}`) ||
                   document.getElementById(boothId) ||
                   document.querySelector(`[data-accordion="${boothId}"]`);

    // If still not found, try content matching
    if (!accordion) {
      const accordions = document.querySelectorAll('.w-dropdown, .accordion-item, [id*="accordion"]');
      accordions.forEach(acc => {
        const text = acc.textContent.toLowerCase();
        if ((boothId === 'nba-decoded' && text.includes('nba decoded')) ||
            (boothId === 'making-the-call' && text.includes('making the call')) ||
            (boothId === 'huddle-up' && text.includes('huddle up')) ||
            (boothId === 'trust-issues' && text.includes('trust issues'))) {
          accordion = acc;
        }
      });
    }

    if (accordion) {
      // Try multiple possible scroll containers
      const accordionWrapper = document.querySelector('.accordion-wrapper');
      const sidebar = document.querySelector('.sidebar');
      const rightPanel = document.querySelector('.right-panel, .panel-right, .sidebar-content');
      const scrollContainer = accordionWrapper || sidebar || rightPanel || document.documentElement;

      if (scrollContainer) {
        let targetScroll;

        if (accordionWrapper) {
          // Scroll to top of accordion wrapper
          targetScroll = 0;
        } else {
          // Calculate position relative to scroll container
          const containerRect = scrollContainer.getBoundingClientRect();
          const accordionRect = accordion.getBoundingClientRect();
          const currentScroll = scrollContainer.scrollTop || window.pageYOffset;

          // Calculate target scroll position
          targetScroll = currentScroll + accordionRect.top - containerRect.top - 20; // 20px offset
        }

        // Ensure target scroll is not negative
        targetScroll = Math.max(0, targetScroll);

        scrollContainer.scrollTo({
          top: targetScroll,
          behavior: 'smooth'
        });
      }
    }
  }
  
  resetBooths() {
    // Clear all booth states
    this.booths.forEach(booth => {
      booth.classList.remove('active', 'completed');
      booth.style.transform = 'scale(1)';
      booth.style.animation = '';
    });
    
    // Close all accordions
    this.accordions.forEach(acc => {
      acc.classList.remove('active');
      this.updateAccordionToggle(acc, false);
    });
  }
  
  // Utility method to get booth status
  getBoothStatus(boothId) {
    const booth = document.querySelector(`[data-booth="${boothId}"]`);
    if (!booth) return 'not-found';
    
    if (booth.classList.contains('completed')) return 'completed';
    if (booth.classList.contains('active')) return 'active';
    return 'default';
  }
  
  // Method to get all completed booths
  getCompletedBooths() {
    const completed = [];
    this.booths.forEach(booth => {
      if (booth.classList.contains('completed')) {
        completed.push(booth.dataset.booth);
      }
    });
    return completed;
  }
  
  // Method to simulate user journey
  simulateCompletion() {
    const boothIds = ['nba-decoded', 'making-call', 'huddle-up', 'trust-issues'];
    let index = 0;
    
    const completeNext = () => {
      if (index < boothIds.length) {
        this.markBoothCompleted(boothIds[index]);
        index++;
        setTimeout(completeNext, 1500);
      }
    };
    
    completeNext();
  }
  
  // Method to check if all booths are completed
  isAllCompleted() {
    return this.getCompletedBooths().length === this.booths.length;
  }
}

// Initialize the dashboard map when DOM is ready
let dashboardMap;

function initializeDashboardMap() {
  if (typeof DashboardMap !== 'undefined') {
    dashboardMap = new DashboardMap();

    // Make it globally accessible for debugging/external control
    window.dashboardMap = dashboardMap;

    // Debug: Log found elements
    console.log('Dashboard Map Debug Info:');
    console.log('Booths found:', dashboardMap.booths.length);
    console.log('Accordions found:', dashboardMap.accordions.length);

    // Add manual booth click handlers for links with hash hrefs
    const boothLinks = document.querySelectorAll('a[href*="#"]');
    boothLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href && href.includes('#') && !href.includes('javascript')) {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const boothId = href.split('#')[1];
          if (boothId && dashboardMap) {
            console.log('Manual booth click:', boothId);
            dashboardMap.handleBoothClick(boothId);
          }
        });
      }
    });

    // Optional: Add keyboard shortcuts for testing
    document.addEventListener('keydown', (e) => {
      // Press 'R' to reset all booths (for testing)
      if (e.key === 'r' || e.key === 'R') {
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          dashboardMap.resetBooths();
          console.log('Dashboard map reset');
        }
      }

      // Press 'S' to simulate completion (for testing)
      if (e.key === 's' || e.key === 'S') {
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          dashboardMap.simulateCompletion();
          console.log('Simulating booth completion');
        }
      }
    });
  }
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeDashboardMap);
} else {
  initializeDashboardMap();
}

// Webflow-specific initialization (runs after Webflow is ready)
if (typeof Webflow !== 'undefined') {
  Webflow.push(function() {
    // Re-initialize after Webflow interactions
    setTimeout(initializeDashboardMap, 100);
  });
}

/* 
 * API Usage Examples:
 * 
 * // Mark a booth as completed
 * window.dashboardMap.markBoothCompleted('nba-decoded');
 * 
 * // Set active booth
 * window.dashboardMap.setActiveBooths(['making-call']);
 * 
 * // Open specific accordion
 * window.dashboardMap.openAccordion('huddle-up');
 * 
 * // Reset all booths
 * window.dashboardMap.resetBooths();
 * 
 * // Get booth status
 * const status = window.dashboardMap.getBoothStatus('trust-issues');
 * 
 * // Get all completed booths
 * const completed = window.dashboardMap.getCompletedBooths();
 * 
 * // Check if all booths are completed
 * const allDone = window.dashboardMap.isAllCompleted();
 * 
 * Keyboard Shortcuts (for testing):
 * - Ctrl/Cmd + R: Reset all booths
 * - Ctrl/Cmd + S: Simulate completion sequence
 */
