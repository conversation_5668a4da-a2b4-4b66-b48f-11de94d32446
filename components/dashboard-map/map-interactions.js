/* Dashboard Interactive Map - JavaScript Functionality */
/* Add this JavaScript to Webflow Page Settings > Custom Code > Footer Code */

// Interactive Map Class
class DashboardMap {
  constructor() {
    this.booths = document.querySelectorAll('.booth-container');
    this.accordions = document.querySelectorAll('.accordion-item');
    this.isInitialized = false;
    this.init();
  }
  
  init() {
    // Wait for DOM to be fully loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
    } else {
      this.setupEventListeners();
    }
  }
  
  setupEventListeners() {
    // Add click handlers for booth containers
    this.booths.forEach(booth => {
      booth.addEventListener('click', (e) => {
        e.preventDefault();
        const boothId = booth.dataset.booth;
        if (boothId) {
          this.handleBoothClick(boothId);
        }
      });
      
      // Add hover effects
      booth.addEventListener('mouseenter', () => {
        if (!booth.classList.contains('active') && !booth.classList.contains('completed')) {
          booth.style.transform = 'scale(1.05)';
        }
      });
      
      booth.addEventListener('mouseleave', () => {
        if (!booth.classList.contains('active') && !booth.classList.contains('completed')) {
          booth.style.transform = 'scale(1)';
        }
      });
    });
    
    // Add click handlers for accordion headers
    this.accordions.forEach(accordion => {
      const header = accordion.querySelector('.accordion-header');
      if (header) {
        header.addEventListener('click', () => {
          this.handleAccordionClick(accordion);
        });
      }
    });
    
    this.isInitialized = true;
    console.log('Dashboard Map initialized successfully');
  }
  
  handleBoothClick(boothId) {
    // Open corresponding accordion
    this.openAccordion(boothId);
    
    // Set booth as active
    this.setActiveBooths([boothId]);
    
    // Scroll accordion into view if needed
    this.scrollToAccordion(boothId);
  }
  
  handleAccordionClick(accordion) {
    const isActive = accordion.classList.contains('active');
    
    // Close all accordions first
    this.accordions.forEach(acc => {
      acc.classList.remove('active');
      this.updateAccordionToggle(acc, false);
    });
    
    // Open clicked accordion if it wasn't active
    if (!isActive) {
      accordion.classList.add('active');
      this.updateAccordionToggle(accordion, true);
      
      // Highlight corresponding booth
      const boothId = accordion.id.replace('accordion-', '');
      this.setActiveBooths([boothId]);
    } else {
      // Clear all booth highlights if closing accordion
      this.setActiveBooths([]);
    }
  }
  
  updateAccordionToggle(accordion, isOpen) {
    const toggle = accordion.querySelector('.accordion-toggle');
    if (toggle) {
      toggle.textContent = isOpen ? '×' : '+';
    }
  }
  
  openAccordion(boothId) {
    // Close all accordions
    this.accordions.forEach(acc => {
      acc.classList.remove('active');
      this.updateAccordionToggle(acc, false);
    });
    
    // Open specific accordion
    const targetAccordion = document.getElementById(`accordion-${boothId}`);
    if (targetAccordion) {
      targetAccordion.classList.add('active');
      this.updateAccordionToggle(targetAccordion, true);
    }
  }
  
  setActiveBooths(boothIds) {
    // Clear all booth states
    this.booths.forEach(booth => {
      booth.classList.remove('active');
      booth.style.transform = 'scale(1)';
    });
    
    // Set active booths
    boothIds.forEach(boothId => {
      const booth = document.querySelector(`[data-booth="${boothId}"]`);
      if (booth && !booth.classList.contains('completed')) {
        booth.classList.add('active');
      }
    });
  }
  
  markBoothCompleted(boothId) {
    const booth = document.querySelector(`[data-booth="${boothId}"]`);
    if (booth) {
      booth.classList.remove('active');
      booth.classList.add('completed');
      booth.style.transform = 'scale(1)';
      
      // Add completion animation
      this.animateCompletion(booth);
    }
  }
  
  animateCompletion(booth) {
    // Add a temporary completion effect
    booth.style.animation = 'none';
    booth.offsetHeight; // Trigger reflow
    booth.style.animation = 'boothCompletedGlow 3s ease-in-out infinite';
  }
  
  scrollToAccordion(boothId) {
    const accordion = document.getElementById(`accordion-${boothId}`);
    if (accordion) {
      // Check if there's an accordion-wrapper, otherwise use sidebar
      const accordionWrapper = document.querySelector('.accordion-wrapper');
      const scrollContainer = accordionWrapper || document.querySelector('.sidebar');

      if (scrollContainer) {
        // If accordion-wrapper exists, scroll to its top
        // Otherwise, scroll to the specific accordion with offset
        let targetScroll;

        if (accordionWrapper) {
          // Scroll to top of accordion wrapper
          targetScroll = 0;
        } else {
          // Original behavior: scroll to specific accordion with offset
          const accordionTop = accordion.offsetTop;
          targetScroll = accordionTop - 20; // 20px offset
        }

        scrollContainer.scrollTo({
          top: targetScroll,
          behavior: 'smooth'
        });
      }
    }
  }
  
  resetBooths() {
    // Clear all booth states
    this.booths.forEach(booth => {
      booth.classList.remove('active', 'completed');
      booth.style.transform = 'scale(1)';
      booth.style.animation = '';
    });
    
    // Close all accordions
    this.accordions.forEach(acc => {
      acc.classList.remove('active');
      this.updateAccordionToggle(acc, false);
    });
  }
  
  // Utility method to get booth status
  getBoothStatus(boothId) {
    const booth = document.querySelector(`[data-booth="${boothId}"]`);
    if (!booth) return 'not-found';
    
    if (booth.classList.contains('completed')) return 'completed';
    if (booth.classList.contains('active')) return 'active';
    return 'default';
  }
  
  // Method to get all completed booths
  getCompletedBooths() {
    const completed = [];
    this.booths.forEach(booth => {
      if (booth.classList.contains('completed')) {
        completed.push(booth.dataset.booth);
      }
    });
    return completed;
  }
  
  // Method to simulate user journey
  simulateCompletion() {
    const boothIds = ['nba-decoded', 'making-call', 'huddle-up', 'trust-issues'];
    let index = 0;
    
    const completeNext = () => {
      if (index < boothIds.length) {
        this.markBoothCompleted(boothIds[index]);
        index++;
        setTimeout(completeNext, 1500);
      }
    };
    
    completeNext();
  }
  
  // Method to check if all booths are completed
  isAllCompleted() {
    return this.getCompletedBooths().length === this.booths.length;
  }
}

// Initialize the dashboard map when DOM is ready
let dashboardMap;

function initializeDashboardMap() {
  if (typeof DashboardMap !== 'undefined') {
    dashboardMap = new DashboardMap();
    
    // Make it globally accessible for debugging/external control
    window.dashboardMap = dashboardMap;
    
    // Optional: Add keyboard shortcuts for testing
    document.addEventListener('keydown', (e) => {
      // Press 'R' to reset all booths (for testing)
      if (e.key === 'r' || e.key === 'R') {
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          dashboardMap.resetBooths();
          console.log('Dashboard map reset');
        }
      }
      
      // Press 'S' to simulate completion (for testing)
      if (e.key === 's' || e.key === 'S') {
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          dashboardMap.simulateCompletion();
          console.log('Simulating booth completion');
        }
      }
    });
  }
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeDashboardMap);
} else {
  initializeDashboardMap();
}

// Webflow-specific initialization (runs after Webflow is ready)
if (typeof Webflow !== 'undefined') {
  Webflow.push(function() {
    // Re-initialize after Webflow interactions
    setTimeout(initializeDashboardMap, 100);
  });
}

/* 
 * API Usage Examples:
 * 
 * // Mark a booth as completed
 * window.dashboardMap.markBoothCompleted('nba-decoded');
 * 
 * // Set active booth
 * window.dashboardMap.setActiveBooths(['making-call']);
 * 
 * // Open specific accordion
 * window.dashboardMap.openAccordion('huddle-up');
 * 
 * // Reset all booths
 * window.dashboardMap.resetBooths();
 * 
 * // Get booth status
 * const status = window.dashboardMap.getBoothStatus('trust-issues');
 * 
 * // Get all completed booths
 * const completed = window.dashboardMap.getCompletedBooths();
 * 
 * // Check if all booths are completed
 * const allDone = window.dashboardMap.isAllCompleted();
 * 
 * Keyboard Shortcuts (for testing):
 * - Ctrl/Cmd + R: Reset all booths
 * - Ctrl/Cmd + S: Simulate completion sequence
 */
