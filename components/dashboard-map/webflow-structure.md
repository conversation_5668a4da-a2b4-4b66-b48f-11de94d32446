# Webflow Structure Guide - Dashboard Interactive Map

## Overview
This guide provides step-by-step instructions for building the HTML structure in Webflow's visual designer. Follow these steps exactly to ensure proper CSS and JavaScript integration.

## Step 1: Main Container Setup

### 1.1 Create Main Container
1. **Add Div Block** → Name it `dashboard-container`
2. **Set Display**: Flexbox
3. **Set Direction**: Horizontal
4. **Set Gap**: 30px
5. **Set Max Width**: 1400px
6. **Set Margin**: Auto (center)
7. **Set Padding**: 20px
8. **Set Height**: calc(100vh - 40px)

### 1.2 Add Custom Class
- **Class Name**: `dashboard-container`
- This will be styled by the CSS embed

## Step 2: Map Container

### 2.1 Create Map Container
1. **Inside dashboard-container**, add **Div Block** → Name it `map-container`
2. **Set Flex**: 1 (to take remaining space)
3. **Set Position**: Relative
4. **Set Border Radius**: 20px
5. **Set Padding**: 20px

### 2.2 Add Custom Class
- **Class Name**: `map-container`

### 2.3 Add Map Background
1. **Inside map-container**, add **Div Block** → Name it `map-background`
2. **Set Position**: Absolute
3. **Set Top**: 0
4. **Set Left**: 0
5. **Set Width**: 100%
6. **Set Height**: 100%

### 2.4 Add Custom Class
- **Class Name**: `map-background`

## Step 3: Booth Containers

### 3.1 NBA DECODED Booth
1. **Inside map-container**, add **Link Block** → Name it `nba-decoded-booth`
2. **Set Position**: Absolute
3. **Set Width**: 120px
4. **Set Height**: 120px
5. **Set Link Settings**: `#nba-decoded`

#### 3.1.1 Add Custom Classes
- **Class Names**: `booth-container booth-nba-decoded`
- **Custom Attribute**: `data-booth="nba-decoded"`

#### 3.1.2 Add Booth Icon
1. **Inside the link**, add **Div Block** → Name it `nba-decoded-icon`
2. **Set Width**: 80px
3. **Set Height**: 80px

**Custom Class**: `booth-icon`

#### 3.1.3 Add Booth Label
1. **Inside the link** (after icon), add **Div Block** → Name it `nba-decoded-label`
2. **Add Text**: "NBA DECODED"

**Custom Class**: `booth-label`

#### 3.1.4 Add Lottie Decorations (Optional)
1. **Inside booth-icon**, add **HTML Embed** → Name it `lottie-1`
2. **Embed Code**:
```html
<lottie-player 
  class="lottie-decoration top-left"
  src="YOUR_LOTTIE_URL_HERE.json"
  background="transparent"
  speed="1"
  loop
  autoplay>
</lottie-player>
```

3. **Add another HTML Embed** → Name it `lottie-2`
4. **Embed Code**:
```html
<lottie-player 
  class="lottie-decoration bottom-right"
  src="YOUR_LOTTIE_URL_HERE.json"
  background="transparent"
  speed="0.8"
  loop
  autoplay>
</lottie-player>
```

### 3.2 MAKING THE CALL Booth
**Repeat Step 3.1** with these changes:
- **Link Settings**: `#making-call`
- **Custom Classes**: `booth-container booth-making-call`
- **Custom Attribute**: `data-booth="making-call"`
- **Label Text**: "MAKING THE CALL"
- **Lottie Classes**: `top-right` and `bottom-left`

### 3.3 HUDDLE UP Booth
**Repeat Step 3.1** with these changes:
- **Link Settings**: `#huddle-up`
- **Custom Classes**: `booth-container booth-huddle-up`
- **Custom Attribute**: `data-booth="huddle-up"`
- **Label Text**: "HUDDLE UP"
- **Lottie Classes**: `top-left` and `top-right`

### 3.4 TRUST ISSUES Booth
**Repeat Step 3.1** with these changes:
- **Link Settings**: `#trust-issues`
- **Custom Classes**: `booth-container booth-trust-issues`
- **Custom Attribute**: `data-booth="trust-issues"`
- **Label Text**: "TRUST ISSUES"
- **Lottie Classes**: `bottom-left` and `bottom-right`

## Step 4: Sidebar Container

### 4.1 Create Sidebar
1. **Inside dashboard-container** (after map-container), add **Div Block** → Name it `sidebar`
2. **Set Width**: 400px
3. **Set Border Radius**: 20px
4. **Set Padding**: 30px

### 4.2 Add Custom Class
- **Class Name**: `sidebar`

### 4.3 Add Sidebar Title
1. **Inside sidebar**, add **Heading (H2)** → Name it `sidebar-title`
2. **Set Text**: "Exhibit Details"

**Custom Class**: `sidebar-title`

## Step 5: Accordion Items

### 5.1 NBA DECODED Accordion
1. **Inside sidebar**, add **Div Block** → Name it `accordion-nba-decoded`
2. **Custom Class**: `accordion-item`
3. **Custom Attribute**: `id="accordion-nba-decoded"`

#### 5.1.1 Accordion Header
1. **Inside accordion item**, add **Div Block** → Name it `accordion-header-nba`
2. **Set Display**: Flexbox
3. **Set Justify**: Space Between
4. **Set Align**: Center

**Custom Class**: `accordion-header`

#### 5.1.2 Header Title
1. **Inside accordion header**, add **Heading (H3)** → Name it `accordion-title-nba`
2. **Set Text**: "NBA DECODED"

**Custom Class**: `accordion-title`

#### 5.1.3 Header Toggle
1. **Inside accordion header** (after title), add **Div Block** → Name it `accordion-toggle-nba`
2. **Set Text**: "+"

**Custom Class**: `accordion-toggle`

#### 5.1.4 Accordion Content
1. **Inside accordion item** (after header), add **Div Block** → Name it `accordion-content-nba`

**Custom Class**: `accordion-content`

#### 5.1.5 Content Paragraph
1. **Inside accordion content**, add **Paragraph** → Name it `accordion-text-nba`
2. **Set Text**: "Discover the secrets behind NBA analytics and data visualization. Learn how teams use advanced statistics to gain competitive advantages."

#### 5.1.6 Content List
1. **Inside accordion content** (after paragraph), add **List** → Name it `accordion-list-nba`
2. **Add List Items**:
   - Interactive data dashboards
   - Player performance metrics
   - Team strategy analysis
   - Real-time game insights

### 5.2 MAKING THE CALL Accordion
**Repeat Step 5.1** with these changes:
- **Div Name**: `accordion-making-call`
- **ID**: `id="accordion-making-call"`
- **Title**: "MAKING THE CALL"
- **Content**: "Step into the shoes of an NBA referee and experience the split-second decisions that shape the game."
- **List Items**:
  - Virtual reality referee training
  - Rule interpretation challenges
  - Instant replay scenarios
  - Communication protocols

### 5.3 HUDDLE UP Accordion
**Repeat Step 5.1** with these changes:
- **Div Name**: `accordion-huddle-up`
- **ID**: `id="accordion-huddle-up"`
- **Title**: "HUDDLE UP"
- **Content**: "Experience team strategy and coaching decisions in high-pressure game situations."
- **List Items**:
  - Strategic play calling
  - Timeout management
  - Player substitutions
  - Game flow analysis

### 5.4 TRUST ISSUES Accordion
**Repeat Step 5.1** with these changes:
- **Div Name**: `accordion-trust-issues`
- **ID**: `id="accordion-trust-issues"`
- **Title**: "TRUST ISSUES"
- **Content**: "Explore the psychology of trust between players, coaches, and organizations in professional basketball."
- **List Items**:
  - Team chemistry dynamics
  - Leadership challenges
  - Communication breakdowns
  - Building team culture

## Step 6: Custom Attributes Summary

Make sure these custom attributes are set correctly:

### Booth Containers
```
data-booth="nba-decoded"     → NBA DECODED booth
data-booth="making-call"     → MAKING THE CALL booth
data-booth="huddle-up"       → HUDDLE UP booth
data-booth="trust-issues"    → TRUST ISSUES booth
```

### Accordion Items
```
id="accordion-nba-decoded"   → NBA DECODED accordion
id="accordion-making-call"   → MAKING THE CALL accordion
id="accordion-huddle-up"     → HUDDLE UP accordion
id="accordion-trust-issues"  → TRUST ISSUES accordion
```

## Step 7: Class Names Checklist

### Container Classes
- [ ] `dashboard-container`
- [ ] `map-container`
- [ ] `map-background`
- [ ] `sidebar`

### Booth Classes
- [ ] `booth-container booth-nba-decoded`
- [ ] `booth-container booth-making-call`
- [ ] `booth-container booth-huddle-up`
- [ ] `booth-container booth-trust-issues`
- [ ] `booth-icon` (for each booth)
- [ ] `booth-label` (for each booth)

### Accordion Classes
- [ ] `accordion-item` (for each accordion)
- [ ] `accordion-header` (for each header)
- [ ] `accordion-title` (for each title)
- [ ] `accordion-toggle` (for each toggle)
- [ ] `accordion-content` (for each content)

### Lottie Classes (if using)
- [ ] `lottie-decoration top-left`
- [ ] `lottie-decoration top-right`
- [ ] `lottie-decoration bottom-left`
- [ ] `lottie-decoration bottom-right`

## Step 8: Final Structure Verification

Your final structure should look like this in the Webflow Navigator:

```
dashboard-container
├── map-container
│   ├── map-background
│   ├── nba-decoded-booth (Link Block)
│   │   ├── nba-decoded-icon
│   │   │   ├── lottie-1 (HTML Embed)
│   │   │   └── lottie-2 (HTML Embed)
│   │   └── nba-decoded-label
│   ├── making-call-booth (Link Block)
│   │   ├── making-call-icon
│   │   │   ├── lottie-1 (HTML Embed)
│   │   │   └── lottie-2 (HTML Embed)
│   │   └── making-call-label
│   ├── huddle-up-booth (Link Block)
│   │   ├── huddle-up-icon
│   │   │   ├── lottie-1 (HTML Embed)
│   │   │   └── lottie-2 (HTML Embed)
│   │   └── huddle-up-label
│   └── trust-issues-booth (Link Block)
│       ├── trust-issues-icon
│       │   ├── lottie-1 (HTML Embed)
│       │   └── lottie-2 (HTML Embed)
│       └── trust-issues-label
└── sidebar
    ├── sidebar-title (H2)
    ├── accordion-nba-decoded
    │   ├── accordion-header-nba
    │   │   ├── accordion-title-nba (H3)
    │   │   └── accordion-toggle-nba
    │   └── accordion-content-nba
    │       ├── accordion-text-nba (Paragraph)
    │       └── accordion-list-nba (List)
    ├── accordion-making-call
    │   ├── accordion-header-making
    │   │   ├── accordion-title-making (H3)
    │   │   └── accordion-toggle-making
    │   └── accordion-content-making
    │       ├── accordion-text-making (Paragraph)
    │       └── accordion-list-making (List)
    ├── accordion-huddle-up
    │   ├── accordion-header-huddle
    │   │   ├── accordion-title-huddle (H3)
    │   │   └── accordion-toggle-huddle
    │   └── accordion-content-huddle
    │       ├── accordion-text-huddle (Paragraph)
    │       └── accordion-list-huddle (List)
    └── accordion-trust-issues
        ├── accordion-header-trust
        │   ├── accordion-title-trust (H3)
        │   └── accordion-toggle-trust
        └── accordion-content-trust
            ├── accordion-text-trust (Paragraph)
            └── accordion-list-trust (List)
```

## Next Steps

1. **Verify Structure**: Check that all elements are properly nested
2. **Add CSS**: Copy the CSS embeds to your page settings
3. **Add JavaScript**: Copy the JS embeds to your page settings
4. **Test Interactions**: Click booths and accordions to verify functionality
5. **Customize Assets**: Replace placeholder content with your actual graphics and animations
