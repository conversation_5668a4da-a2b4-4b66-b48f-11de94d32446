# Webflow Embed Codes - Dashboard Interactive Map

## Complete Copy-Paste Embeds for Webflow

### 1. Head Code (Page Settings > Custom Code > Head Code)

Copy and paste this entire block into your Webflow page's Head Code section:

```html
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Satoshi:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
/* Dashboard Interactive Map - Main Styles */
.dashboard-container {
  display: flex;
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  height: calc(100vh - 40px);
  font-family: 'Satoshi', -apple-system, BlinkMacSystemFont, sans-serif;
}

.map-container {
  flex: 1;
  position: relative;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  padding: 20px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.map-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('YOUR_MAP_SVG_URL_HERE');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.3;
  z-index: 1;
}

.booth-container {
  position: absolute;
  width: 120px;
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  border-radius: 50%;
  text-decoration: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.booth-nba-decoded {
  background: radial-gradient(circle, rgba(169, 143, 88, 0.2) 0%, transparent 70%);
}

.booth-making-call {
  background: radial-gradient(circle, rgba(14, 248, 248, 0.2) 0%, transparent 70%);
}

.booth-huddle-up {
  background: radial-gradient(circle, rgba(255, 54, 88, 0.2) 0%, transparent 70%);
}

.booth-trust-issues {
  background: radial-gradient(circle, rgba(169, 143, 88, 0.2) 0%, transparent 70%);
}

.booth-icon {
  width: 80px;
  height: 80px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
  border: 3px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  margin-bottom: 8px;
}

.booth-label {
  font-size: 11px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.booth-container:hover {
  transform: scale(1.05);
}

.booth-container:hover .booth-icon {
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

.booth-container.active {
  animation: boothActivePulse 2s ease-in-out infinite;
}

.booth-container.active .booth-icon {
  border-color: #FF3658;
  box-shadow: 0 0 30px rgba(255, 54, 88, 0.6);
}

.booth-container.completed {
  animation: boothCompletedGlow 3s ease-in-out infinite;
}

.booth-container.completed .booth-icon {
  border-color: #0EF8F8;
  box-shadow: 0 0 30px rgba(14, 248, 248, 0.6);
}

.lottie-decoration {
  position: absolute;
  width: 24px;
  height: 24px;
  pointer-events: none;
  z-index: 5;
}

.lottie-decoration.top-left {
  top: -8px;
  left: -8px;
}

.lottie-decoration.top-right {
  top: -8px;
  right: -8px;
}

.lottie-decoration.bottom-left {
  bottom: -8px;
  left: -8px;
}

.lottie-decoration.bottom-right {
  bottom: -8px;
  right: -8px;
}

.sidebar {
  width: 400px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 20px;
  padding: 30px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow-y: auto;
  max-height: calc(100vh - 40px);
}

.sidebar h2 {
  color: #0EF8F8;
  margin: 0 0 30px 0;
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.accordion-item {
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.accordion-item.active {
  border-color: #0EF8F8;
  box-shadow: 0 0 20px rgba(14, 248, 248, 0.2);
}

.accordion-header {
  background: rgba(36, 36, 36, 0.8);
  padding: 20px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.accordion-header:hover {
  background: rgba(36, 36, 36, 1);
}

.accordion-item.active .accordion-header {
  background: rgba(14, 248, 248, 0.1);
}

.accordion-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
  letter-spacing: 0.1em;
}

.accordion-toggle {
  font-size: 24px;
  font-weight: bold;
  color: #0EF8F8;
  transition: transform 0.3s ease;
}

.accordion-item.active .accordion-toggle {
  transform: rotate(45deg);
}

.accordion-content {
  padding: 0 20px;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.5);
}

.accordion-item.active .accordion-content {
  padding: 20px;
  max-height: 300px;
}

.accordion-content p {
  margin: 0 0 15px 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.accordion-content ul {
  margin: 0;
  padding-left: 20px;
  color: rgba(255, 255, 255, 0.7);
}

.accordion-content li {
  margin-bottom: 8px;
  line-height: 1.4;
}

@keyframes boothActivePulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 15px rgba(255, 54, 88, 0.6));
  }
  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 0 25px rgba(255, 54, 88, 0.9));
  }
}

@keyframes boothCompletedGlow {
  0%, 100% {
    filter: drop-shadow(0 0 15px rgba(14, 248, 248, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 25px rgba(14, 248, 248, 0.9));
  }
}

@media (max-width: 1440px) {
  .dashboard-container {
    max-width: 1200px;
    gap: 20px;
  }

  .sidebar {
    width: 350px;
    padding: 25px;
  }

  .booth-container {
    width: 100px;
    height: 100px;
  }

  .booth-icon {
    width: 70px;
    height: 70px;
  }
}

@media (max-width: 1024px) {
  .dashboard-container {
    flex-direction: column;
    height: auto;
    gap: 20px;
  }

  .sidebar {
    width: 100%;
    max-height: 400px;
  }

  .map-container {
    height: 500px;
  }
}
</style>

<style>
/* Booth Icon Backgrounds - Replace URLs with your custom icons */
.booth-nba-decoded .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0zMCAzMEg2MFY2MEgzMFYzMFoiIGZpbGw9IiMwMDAiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8yOF80NDgiIHgxPSI0NS41IiB5MT0iMCIgeDI9IjQ1LjUiIHkyPSI5MSIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkYzNjU4Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzBFRjhGOCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');
}

.booth-making-call .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0yNSAzNUg2NVY1NUgyNVYzNVoiIGZpbGw9IiMwMDAiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8yOF80NDgiIHgxPSI0NS41IiB5MT0iMCIgeDI9IjQ1LjUiIHkyPSI5MSIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkYzNjU4Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzBFRjhGOCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');
}

.booth-huddle-up .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxyZWN0IHg9IjIwIiB5PSIzNSIgd2lkdGg9IjUwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMDAwIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMjhfNDQ4IiB4MT0iNDUuNSIgeTE9IjAiIHgyPSI0NS41IiB5Mj0iOTEiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI0ZGMzY1OCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwRUY4RjgiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');
}

.booth-trust-issues .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0zNSAyNUg1NVY2NUgzNVYyNVoiIGZpbGw9IiMwMDAiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8yOF80NDgiIHgxPSI0NS41IiB5MT0iMCIgeDI9IjQ1LjUiIHkyPSI5MSIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkYzNjU4Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzBFRjhGOCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');
}

.booth-nba-decoded.completed .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0zMCAzMEg2MFY2MEgzMFYzMFoiIGZpbGw9IiMwMDAiLz4KPHBhdGggZD0iTTM4IDQ1TDQzIDUwTDUzIDQwIiBzdHJva2U9IiMwRUY4RjgiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMjhfNDQ4IiB4MT0iNDUuNSIgeTE9IjAiIHgyPSI0NS41IiB5Mj0iOTEiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI0ZGMzY1OCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwRUY4RjgiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');
}

.booth-making-call.completed .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0yNSAzNUg2NVY1NUgyNVYzNVoiIGZpbGw9IiMwMDAiLz4KPHBhdGggZD0iTTM4IDQ1TDQzIDUwTDUzIDQwIiBzdHJva2U9IiMwRUY4RjgiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMjhfNDQ4IiB4MT0iNDUuNSIgeTE9IjAiIHgyPSI0NS41IiB5Mj0iOTEiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI0ZGMzY1OCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwRUY4RjgiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');
}

.booth-huddle-up.completed .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxyZWN0IHg9IjIwIiB5PSIzNSIgd2lkdGg9IjUwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMDAwIi8+CjxwYXRoIGQ9Ik0zOCA0NUw0MyA1MEw1MyA0MCIgc3Ryb2tlPSIjMEVGOEY4IiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzI4XzQ0OCIgeDE9IjQ1LjUiIHkxPSIwIiB4Mj0iNDUuNSIgeTI9IjkxIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiNGRjM2NTgiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjMEVGOEY4Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+Cg==');
}

.booth-trust-issues.completed .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0zNSAyNUg1NVY2NUgzNVYyNVoiIGZpbGw9IiMwMDAiLz4KPHBhdGggZD0iTTM4IDQ1TDQzIDUwTDUzIDQwIiBzdHJva2U9IiMwRUY4RjgiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMjhfNDQ4IiB4MT0iNDUuNSIgeTE9IjAiIHgyPSI0NS41IiB5Mj0iOTEiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI0ZGMzY1OCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwRUY4RjgiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');
}
</style>
```

### 2. Footer Code (Page Settings > Custom Code > Footer Code)

Copy and paste this entire block into your Webflow page's Footer Code section:

```html
<!-- Lottie Player Library (optional - only if using Lottie animations) -->
<script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>

<script>
/* Dashboard Interactive Map - JavaScript Functionality */
class DashboardMap {
  constructor() {
    this.isInitialized = false;
    this.init();
  }

  init() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
    } else {
      this.setupEventListeners();
    }
  }

  setupEventListeners() {
    // Find all booth links with hash hrefs
    const boothLinks = document.querySelectorAll('a[href*="#"]');
    console.log('Found booth links:', boothLinks.length);

    boothLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href && href.includes('#') && !href.includes('javascript')) {
        console.log('Setting up listener for:', href);

        link.addEventListener('click', (e) => {
          e.preventDefault();
          const boothId = href.split('#')[1];
          console.log('Booth clicked:', boothId);

          if (boothId) {
            this.handleBoothClick(boothId);
          }
        });
      }
    });

    this.isInitialized = true;
    console.log('Dashboard Map initialized successfully');
  }

  handleBoothClick(boothId) {
    console.log('Handling booth click for:', boothId);
    this.openAccordion(boothId);

    // Wait for accordion animation to complete before scrolling
    setTimeout(() => {
      this.scrollToAccordion(boothId);
    }, 350); // Wait slightly longer than the 0.28s transition
  }

  openAccordion(boothId) {
    console.log('Looking for accordion for booth:', boothId);

    // Close all accordions first
    const allAccordions = document.querySelectorAll('.accordion-item');
    allAccordions.forEach(accordion => {
      accordion.classList.remove('open');
      const content = accordion.querySelector('.accordion-content');
      if (content) {
        content.style.height = '0px';
      }
    });

    // Find target accordion by text content matching
    let targetAccordion = null;
    const accordions = document.querySelectorAll('.accordion-item');

    accordions.forEach(accordion => {
      const text = accordion.textContent.toLowerCase();
      console.log('Checking accordion text:', text.substring(0, 50) + '...');

      if ((boothId === 'nba-decoded' && text.includes('nba decoded')) ||
          (boothId === 'making-the-call' && text.includes('making the call')) ||
          (boothId === 'huddle-up' && text.includes('huddle up')) ||
          (boothId === 'trust-issues' && text.includes('trust issues'))) {
        targetAccordion = accordion;
        console.log('Found matching accordion for:', boothId);
      }
    });

    // Open the target accordion
    if (targetAccordion) {
      console.log('Opening accordion');
      targetAccordion.classList.add('open');
      const content = targetAccordion.querySelector('.accordion-content');
      if (content) {
        content.style.height = content.scrollHeight + 'px';
      }
    } else {
      console.log('No matching accordion found for:', boothId);
    }
  }

  // Utility methods for future use
  markBoothCompleted(boothId) {
    console.log('Marking booth completed:', boothId);
  }

  scrollToAccordion(boothId) {
    console.log('Scrolling to accordion for:', boothId);

    // Find the specific accordion that was opened
    let targetAccordion = null;
    const accordions = document.querySelectorAll('.accordion-item');

    accordions.forEach(accordion => {
      const text = accordion.textContent.toLowerCase();
      if ((boothId === 'nba-decoded' && text.includes('nba decoded')) ||
          (boothId === 'making-the-call' && text.includes('making the call')) ||
          (boothId === 'huddle-up' && text.includes('huddle up')) ||
          (boothId === 'trust-issues' && text.includes('trust issues'))) {
        targetAccordion = accordion;
      }
    });

    if (!targetAccordion) {
      console.log('No target accordion found for scrolling');
      return;
    }

    // Find the accordion wrapper
    const accordionWrapper = document.querySelector('.accordion-wrapper');

    if (accordionWrapper) {
      console.log('Found accordion wrapper and target accordion!');
      console.log('Wrapper scrollTop before:', accordionWrapper.scrollTop);

      // Calculate the position of the target accordion relative to the wrapper
      const wrapperRect = accordionWrapper.getBoundingClientRect();
      const accordionRect = targetAccordion.getBoundingClientRect();

      // Calculate how much to scroll to bring the accordion to the top of the wrapper
      const scrollAmount = accordionWrapper.scrollTop + (accordionRect.top - wrapperRect.top);

      console.log('Scrolling accordion to top of wrapper, scroll amount:', scrollAmount);

      // Scroll the wrapper so the target accordion appears at the top
      accordionWrapper.scrollTo({
        top: scrollAmount,
        behavior: 'smooth'
      });

      // Check if scroll worked
      setTimeout(() => {
        console.log('Wrapper scrollTop after:', accordionWrapper.scrollTop);
      }, 500);

      return;
    }

    console.log('No accordion wrapper found!');

    // If no wrapper, try to find a scrollable container with accordions
    const accordionContainer = document.querySelector('.accordion-container, .sidebar, .right-panel, [class*="accordion"]');

    if (accordionContainer) {
      console.log('Found accordion container, scrolling to top...');
      accordionContainer.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
      return;
    }

    // Fallback: find the first accordion and scroll to it
    const firstAccordion = document.querySelector('.accordion-item');
    if (firstAccordion) {
      console.log('Scrolling to first accordion...');
      firstAccordion.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    } else {
      console.log('No accordion elements found for scrolling');
    }
  }

  resetBooths() {
    console.log('Resetting all booths');
    // Close all accordions
    const allAccordions = document.querySelectorAll('.accordion-item');
    allAccordions.forEach(accordion => {
      accordion.classList.remove('open');
      const content = accordion.querySelector('.accordion-content');
      if (content) {
        content.style.height = '0px';
      }
    });
  }
}

let dashboardMap;

function initializeDashboardMap() {
  console.log('Initializing Dashboard Map...');

  if (typeof DashboardMap !== 'undefined') {
    dashboardMap = new DashboardMap();
    window.dashboardMap = dashboardMap;

    // Debug: Log found elements
    const boothLinks = document.querySelectorAll('a[href*="#"]');
    const accordions = document.querySelectorAll('.accordion-item');
    console.log('Dashboard Map Debug Info:');
    console.log('Booth links found:', boothLinks.length);
    console.log('Accordion items found:', accordions.length);

    // Test keyboard shortcut
    document.addEventListener('keydown', (e) => {
      if (e.key === 'r' || e.key === 'R') {
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          dashboardMap.resetBooths();
          console.log('Dashboard map reset');
        }
      }
    });
  } else {
    console.error('DashboardMap class not found');
  }
}

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeDashboardMap);
} else {
  initializeDashboardMap();
}

if (typeof Webflow !== 'undefined') {
  Webflow.push(function() {
    setTimeout(initializeDashboardMap, 100);
  });
}

// TOOLTIP FUNCTIONALITY FOR SIDE QUESTS
function initializeTooltips() {
  console.log('Initializing tooltips...');

  // First, let's see what side quest elements exist
  const allSideQuestItems = document.querySelectorAll('.side-quest-list');
  console.log('All side quest items found:', allSideQuestItems.length);

  // Log the structure of each item
  allSideQuestItems.forEach((item, index) => {
    console.log(`Side quest item ${index + 1}:`, item);
    console.log(`  - Has tooltip-box:`, !!item.querySelector('.tooltip-box'));
    console.log(`  - Text content:`, item.textContent.trim().substring(0, 50));

    // Add cursor pointer style to all side quest items
    item.style.cursor = 'pointer';
  });

  // Only target side quest items that actually have tooltip boxes
  const sideQuestItemsWithTooltips = document.querySelectorAll('.side-quest-list .tooltip-box');
  console.log('Found side quest items with tooltips:', sideQuestItemsWithTooltips.length);

  // If no tooltips found, let's try a different approach
  if (sideQuestItemsWithTooltips.length === 0) {
    console.log('No tooltip boxes found, setting up basic click handlers for all side quest items');

    allSideQuestItems.forEach((item, index) => {
      item.addEventListener('click', (e) => {
        e.stopPropagation();
        console.log(`Side quest item ${index + 1} clicked (no tooltip)`);
        console.log('Item element:', item);
      });
    });
    return;
  }

  sideQuestItemsWithTooltips.forEach((tooltip, index) => {
    const item = tooltip.closest('.side-quest-list');
    if (!item) return;

    console.log(`Setting up tooltip for item ${index + 1}`);

    const closeBtn = tooltip.querySelector('.tooltip-close');
    if (!closeBtn) {
      console.log(`No tooltip-close found in item ${index + 1}`);
    }

    // Ensure tooltip starts hidden
    tooltip.style.display = 'none';
    tooltip.style.opacity = '0';

    // Track if tooltip is currently visible
    let isVisible = false;

    // Open tooltip on click
    item.addEventListener('click', (e) => {
      e.stopPropagation();
      console.log(`Side quest item ${index + 1} clicked, currently visible:`, isVisible);

      // Hide other tooltips first
      document.querySelectorAll('.tooltip-box').forEach(tip => {
        if (tip !== tooltip) {
          hideTooltip(tip);
        }
      });

      // Toggle this tooltip
      if (!isVisible) {
        console.log('Showing tooltip');
        showTooltip(tooltip);
        isVisible = true;
      } else {
        console.log('Hiding tooltip');
        hideTooltip(tooltip);
        isVisible = false;
      }
    });

    // Close tooltip on close button click
    if (closeBtn) {
      closeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        console.log('Close button clicked');
        hideTooltip(tooltip);
        isVisible = false;
      });
    }

    // Function to show tooltip
    function showTooltip(tooltip) {
      tooltip.style.display = 'block';

      if (typeof gsap !== 'undefined') {
        gsap.fromTo(tooltip,
          { opacity: 0, y: -10 },
          { opacity: 1, y: 0, duration: 0.3 }
        );
      } else {
        tooltip.style.opacity = '1';
        tooltip.style.transform = 'translateY(0)';
      }
    }

    // Function to hide tooltip
    function hideTooltip(tooltip) {
      if (tooltip.style.display === 'none') return;

      if (typeof gsap !== 'undefined') {
        gsap.to(tooltip, {
          opacity: 0,
          y: -10,
          duration: 0.3,
          onComplete: () => {
            tooltip.style.display = 'none';
          }
        });
      } else {
        tooltip.style.opacity = '0';
        tooltip.style.transform = 'translateY(-10px)';
        setTimeout(() => {
          tooltip.style.display = 'none';
        }, 300);
      }
    }
  });

  // Close tooltip when clicking outside (only once)
  let outsideClickHandler = (e) => {
    // Check if click is outside any tooltip or side quest item
    if (!e.target.closest('.side-quest-list') && !e.target.closest('.tooltip-box')) {
      console.log('Clicked outside, hiding all tooltips');
      document.querySelectorAll('.tooltip-box').forEach(tip => {
        tip.style.opacity = '0';
        tip.style.transform = 'translateY(-10px)';
        setTimeout(() => {
          tip.style.display = 'none';
        }, 300);
      });
    }
  };

  // Remove any existing handler and add new one
  document.removeEventListener('click', outsideClickHandler);
  document.addEventListener('click', outsideClickHandler);

  console.log('Tooltips initialized successfully');
}

// Initialize tooltips after DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initializeTooltips, 200); // Small delay to ensure elements are ready
  });
} else {
  setTimeout(initializeTooltips, 200);
}

// Also initialize after Webflow is ready
if (typeof Webflow !== 'undefined') {
  Webflow.push(function() {
    setTimeout(initializeTooltips, 300);
  });
}
</script>
```

## Important Notes

### 1. Replace Placeholder Content

**Map Background:**
- Replace `YOUR_MAP_SVG_URL_HERE` with your actual map SVG URL
- Example: `https://uploads-ssl.webflow.com/your-site-id/your-map.svg`

**Booth Icons:**
- The current icons are placeholder base64 SVGs
- Replace with your custom booth icons
- Create both default and completed (with checkmarks) versions

### 2. Lottie Animations (Optional)

If you want to use Lottie animations, add them to your booth HTML embeds:

```html
<lottie-player
  class="lottie-decoration top-left"
  src="https://assets1.lottiefiles.com/your-animation.json"
  background="transparent"
  speed="1"
  loop
  autoplay>
</lottie-player>
```

### 3. Testing Commands

Once implemented, you can test with these keyboard shortcuts:
- **Ctrl/Cmd + R**: Reset all booths
- **Ctrl/Cmd + S**: Simulate completion sequence

### 4. API Usage

Control the map programmatically:

```javascript
// Mark booth as completed
window.dashboardMap.markBoothCompleted('nba-decoded');

// Set active booth
window.dashboardMap.setActiveBooths(['making-call']);

// Open specific accordion
window.dashboardMap.openAccordion('huddle-up');

// Reset all booths
window.dashboardMap.resetBooths();

// Get booth status
const status = window.dashboardMap.getBoothStatus('trust-issues');

// Get all completed booths
const completed = window.dashboardMap.getCompletedBooths();

// Check if all booths are completed
const allDone = window.dashboardMap.isAllCompleted();
```

### 5. Customization

**Colors:**
- Primary Cyan: `#0EF8F8`
- Primary Red: `#FF3658`
- Update these in the CSS if needed

**Booth Positions:**
- Adjust the `top`, `left`, `right` percentages in the CSS
- Test on your target devices (Surface Go 4, iPad 11-inch)

**Animation Timing:**
- Modify animation durations in the `@keyframes` sections
- Adjust transition speeds in the `.booth-container` styles