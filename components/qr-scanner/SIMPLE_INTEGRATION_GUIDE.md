# Simple QR Scanner Integration Guide

## Overview
This simplified QR scanner works like a standard QR code reader:
- **URLs** → Shows "Visit Website" button + "Copy URL" button
- **Text** → Displays the text + "Copy Text" button
- **No complex parsing** → Just reads whatever is in the QR code

## Quick Setup for Webflow

### 1. Replace JavaScript
Replace your current QR scanner JavaScript with the simplified version:

```html
<!-- In Webflow embed or before </body> -->
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
<script src="qr-scanner-simple.js"></script>
```

### 2. Use Updated CSS
The CSS has been updated to include styling for the new action buttons. Make sure you're using the latest `qr-scanner-styles.css`.

### 3. HTML Structure (Same as Before)
```html
<div class="qr-scanner-container">
  <div class="qr-scanner-video" id="qr-video"></div>
  
  <div class="qr-scanner-buttons">
    <button class="qr-scanner-start-button">Start Scanning</button>
    <button class="qr-scanner-stop-button">Stop Scanning</button>
    <button class="qr-scanner-switch-camera">Switch Camera</button>
    <button class="qr-scanner-toggle-flash">Toggle Flash</button>
  </div>
  
  <div class="qr-scanner-result"></div>
  <div class="qr-scanner-error"></div>
  <div class="qr-scanner-status"></div>
</div>
```

## How It Works

### URL QR Codes
When scanning a URL (like `https://google.com`), you'll see:
```
🔗 Website Found:
https://google.com
[🌐 Visit Website] [📋 Copy URL]
```

### Text QR Codes  
When scanning text (like `Hello World`), you'll see:
```
📄 Text Content:
Hello World
[📋 Copy Text]
```

## Key Differences from Complex Version

| Feature | Complex Version | Simple Version |
|---------|----------------|----------------|
| **QR Type Detection** | Looks for specific NBA Atlas formats | Detects URLs vs text automatically |
| **Booth Types** | Referee, Media, Coaching specific logic | Universal scanner |
| **Data Processing** | Complex JSON parsing with validation | Simple content detection |
| **User Experience** | Shows different UI based on QR type | Consistent UI with action buttons |
| **Error Handling** | Type-specific error messages | Generic "scan any QR code" approach |

## Customization Options

### Auto-Redirect URLs
To automatically open URLs instead of showing a button, uncomment this section in the JavaScript:

```javascript
// Auto-redirect option (uncomment if you want automatic redirects)
setTimeout(() => {
  if (confirm(`Open ${url}?`)) {
    window.open(fullURL, '_blank');
  }
}, 1000);
```

### Custom Event Handling
Listen for scan events to add your own logic:

```javascript
document.querySelector('.qr-scanner-container').addEventListener('qr-code-scanned', function(event) {
  const { content, result } = event.detail;
  console.log('Scanned:', content);
  
  // Add your custom logic here
  // - Send to analytics
  // - Log to server
  // - Show custom UI
});
```

### Button Customization
Modify the button text/styling by editing the JavaScript functions:

```javascript
// In showURLResult function
<button class="qr-visit-button" onclick="window.open('${fullURL}', '_blank')">
  🌐 Visit Website  <!-- Change this text -->
</button>
```

## Testing

### Test URLs
- `https://google.com`
- `https://apple.com`
- `www.github.com` (without protocol)

### Test Text
- `Hello World`
- `Contact: <EMAIL>`
- `Phone: ******-123-4567`

## Browser Console
The simplified version provides cleaner logging:
```
QR code scanned: https://google.com
QR Scanner Status: Website QR code detected!
```

## Benefits of Simple Version

1. **Universal Compatibility** - Works with any QR code
2. **Intuitive UX** - Clear action buttons for URLs
3. **Less Complex** - No booth types or data validation
4. **Better Performance** - Simpler parsing logic
5. **Easier Debugging** - Cleaner console output

## Migration from Complex Version

If you're switching from the complex version:

1. **Replace JavaScript file** - Use `qr-scanner-simple.js`
2. **Update CSS** - Use the updated styles with button styling
3. **Remove booth-specific logic** - No longer needed
4. **Test with various QR codes** - URLs and text content

## Troubleshooting

### "QR Code: Null" Issue
The simplified version handles this better by:
- Always showing the raw scanned content if parsing fails
- Better null/undefined checking
- Cleaner error messages

### Button Not Working
- Check browser console for JavaScript errors
- Verify the HTML structure matches the expected format
- Ensure the CSS is loaded properly

### Camera Issues
- Same troubleshooting as before (permissions, lighting, etc.)
- The core camera functionality remains the same

This simplified approach should give you exactly what you're looking for - a straightforward QR scanner that handles URLs with action buttons and displays text content clearly!
