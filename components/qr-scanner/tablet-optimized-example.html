<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <title>QR Scanner - Tablet Optimized</title>
  
  <!-- Prevent zoom and ensure landscape orientation -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  
  <!-- Include the HTML5 QR Code library -->
  <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
  
  <!-- Include the optimized QR Scanner styles -->
  <link rel="stylesheet" href="qr-scanner-styles.css">
  
  <style>
    /* Additional tablet-specific optimizations */
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f5f5f7;
      overflow: hidden; /* Prevent scrolling on tablets */
      height: 100vh;
      width: 100vw;
    }
    
    /* Force landscape orientation message */
    .orientation-message {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #000;
      color: white;
      text-align: center;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      z-index: 9999;
      font-size: 24px;
    }
    
    @media screen and (orientation: portrait) {
      .orientation-message {
        display: flex;
      }
      .main-content {
        display: none;
      }
    }
    
    .main-content {
      height: 100vh;
      width: 100vw;
      display: flex;
      flex-direction: column;
    }
    
    /* Header for tablet UI */
    .tablet-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px 24px;
      text-align: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      flex-shrink: 0;
    }
    
    .tablet-header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 700;
    }
    
    .tablet-header p {
      margin: 8px 0 0 0;
      opacity: 0.9;
      font-size: 16px;
    }
    
    /* Scanner container takes remaining space */
    .scanner-wrapper {
      flex: 1;
      display: flex;
      padding: 20px;
      background-color: #f5f5f7;
    }
    
    /* Override container styles for full-screen tablet experience */
    .qr-scanner-container {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      margin: 0;
      flex: 1;
    }
    
    /* Status bar for scan results */
    .scan-status-bar {
      background-color: rgba(255, 255, 255, 0.95);
      padding: 12px 20px;
      border-radius: 12px;
      margin: 16px;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 16px rgba(0,0,0,0.1);
      display: none;
    }
    
    .scan-status-bar.success {
      background-color: rgba(52, 199, 89, 0.1);
      border: 2px solid #34C759;
      color: #1D4ED8;
    }
    
    .scan-status-bar.error {
      background-color: rgba(255, 59, 48, 0.1);
      border: 2px solid #FF3B30;
      color: #DC2626;
    }
  </style>
</head>
<body>
  <!-- Orientation message for portrait mode -->
  <div class="orientation-message">
    <div>
      <h2>📱 ➡️ 📱</h2>
      <p>Please rotate your device to landscape mode</p>
      <p style="font-size: 16px; opacity: 0.8; margin-top: 20px;">
        This QR scanner is optimized for landscape orientation
      </p>
    </div>
  </div>

  <!-- Main content -->
  <div class="main-content">
    <!-- Header -->
    <div class="tablet-header">
      <h1>🏀 NBA Atlas QR Scanner</h1>
      <p>Optimized for Microsoft Surface Go 4 & iPad 11-inch</p>
    </div>

    <!-- Scanner wrapper -->
    <div class="scanner-wrapper">
      <!-- QR Scanner Container -->
      <div class="qr-scanner-container" data-scan-mode="continuous" data-booth-type="referee">
        <!-- Video element for camera feed -->
        <div class="qr-scanner-video" id="qr-video"></div>
        
        <!-- Buttons -->
        <div class="qr-scanner-buttons">
          <button class="qr-scanner-start-button">Start Scanning</button>
          <button class="qr-scanner-stop-button">Stop Scanning</button>
          <button class="qr-scanner-switch-camera">Switch Camera</button>
          <button class="qr-scanner-toggle-flash">Toggle Flash</button>
        </div>
        
        <!-- Status displays -->
        <div class="qr-scanner-result"></div>
        <div class="qr-scanner-error"></div>
        <div class="qr-scanner-status"></div>
        <div class="qr-scanner-offline-indicator">Offline Mode</div>
      </div>
    </div>
    
    <!-- Scan status bar -->
    <div class="scan-status-bar" id="scan-status-bar">
      <strong>Last Scan:</strong> <span id="last-scan-content">No scans yet</span>
    </div>
  </div>

  <!-- Include the optimized QR Scanner script -->
  <script src="qr-scanner_webflow-embed-modified.js"></script>
  
  <script>
    // Additional tablet-specific enhancements
    document.addEventListener('DOMContentLoaded', function() {
      const qrScannerContainer = document.querySelector('.qr-scanner-container');
      const scanStatusBar = document.getElementById('scan-status-bar');
      const lastScanContent = document.getElementById('last-scan-content');
      
      // Show scan status bar
      if (scanStatusBar) {
        scanStatusBar.style.display = 'block';
      }
      
      // Listen for QR code scans
      if (qrScannerContainer) {
        qrScannerContainer.addEventListener('qr-code-scanned', function(event) {
          const { result, boothType } = event.detail;
          console.log('QR code scanned:', result, boothType);
          
          // Update status bar
          if (lastScanContent && scanStatusBar) {
            let displayText = '';
            if (typeof result === 'object') {
              displayText = `${result.type || 'QR'}: ${result.data?.title || result.data?.name || JSON.stringify(result.data)}`;
            } else {
              displayText = `QR Code: ${result}`;
            }
            
            lastScanContent.textContent = displayText;
            scanStatusBar.className = 'scan-status-bar success';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
              scanStatusBar.className = 'scan-status-bar';
            }, 5000);
          }
        });
      }
      
      // Prevent zoom on double tap (iOS Safari)
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
      
      // Lock orientation to landscape if possible
      if (screen.orientation && screen.orientation.lock) {
        screen.orientation.lock('landscape').catch(err => {
          console.log('Orientation lock not supported:', err);
        });
      }
    });
  </script>
</body>
</html>
