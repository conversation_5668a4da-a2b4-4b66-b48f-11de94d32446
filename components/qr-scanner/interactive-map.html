<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive Exhibit Map</title>

  <!-- Lottie Animation Library -->
  <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>

  <style>
    body {
      font-family: 'Satoshi', Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      color: white;
      min-height: 100vh;
    }

    .demo-container {
      max-width: 1400px;
      margin: 0 auto;
      display: flex;
      gap: 30px;
      height: calc(100vh - 40px);
    }

    /* Interactive Map Container */
    .map-container {
      flex: 1;
      position: relative;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 20px;
      padding: 20px;
      border: 2px solid rgba(255, 255, 255, 0.1);
      overflow: hidden;
    }

    /* SVG Map Background */
    .map-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTg3OCIgaGVpZ2h0PSI3NjgiIHZpZXdCb3g9IjAgMCAxODc4IDc2OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTAgMEgxODc4VjU1MkgxMjI5VjU1MkgwVjBaIiBzdHJva2U9InVybCgjcGFpbnQwX2xpbmVhcl8xMjdfMjA4KSIgc3Ryb2tlLXdpZHRoPSIxMCIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzEyN18yMDgiIHgxPSItNi43MzQ5IiB5MT0iMzg0IiB4Mj0iMTg4NC43MyIgeTI9IjM4NCIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkYzNjU4Ii8+CjxzdG9wIG9mZnNldD0iMC4xIiBzdG9wLWNvbG9yPSIjRjkzQTVCIi8+CjxzdG9wIG9mZnNldD0iMC4yMyIgc3RvcC1jb2xvcj0iI0U4NDg2NiIvPgo8c3RvcCBvZmZzZXQ9IjAuMzciIHN0b3AtY29sb3I9IiNDRDVENzgiLz4KPHN0b3Agb2Zmc2V0PSIwLjUzIiBzdG9wLWNvbG9yPSIjQTc3QzkyIi8+CjxzdG9wIG9mZnNldD0iMC43IiBzdG9wLWNvbG9yPSIjNzZBM0IyIi8+CjxzdG9wIG9mZnNldD0iMC44OCIgc3RvcC1jb2xvcj0iIzNCRDNEOSIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwRUY4RjgiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      opacity: 0.3;
      z-index: 1;
    }

    /* Booth Container Base Styles */
    .booth-container {
      position: absolute;
      width: 120px;
      height: 120px;
      cursor: pointer;
      transition: all 0.3s ease;
      z-index: 10;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .booth-container:hover {
      transform: scale(1.1);
      z-index: 20;
    }

    /* Booth Icon Styling */
    .booth-icon {
      width: 80px;
      height: 80px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      border-radius: 50%;
      position: relative;
      transition: all 0.3s ease;
      border: 3px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    /* Booth States */
    .booth-container.completed .booth-icon {
      border-color: #0EF8F8;
      box-shadow: 0 0 20px rgba(14, 248, 248, 0.4);
    }

    .booth-container.active .booth-icon {
      border-color: #FF3658;
      box-shadow: 0 0 20px rgba(255, 54, 88, 0.4);
      animation: activePulse 2s ease-in-out infinite;
    }

    @keyframes activePulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    /* Individual Booth Positions (based on Figma layout) */
    .booth-nba-decoded {
      top: 40%;
      right: 8%;
      background: radial-gradient(circle, rgba(169, 143, 88, 0.2) 0%, transparent 70%);
    }

    .booth-nba-decoded .booth-icon {
      background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0zMCAzMEg2MFY2MEgzMFYzMFoiIGZpbGw9IiMwMDAiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8yOF80NDgiIHgxPSI0NS41IiB5MT0iMCIgeDI9IjQ1LjUiIHkyPSI5MSIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkYzNjU4Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzBFRjhGOCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');
    }

    .booth-making-call {
      top: 6%;
      right: 13%;
      background: radial-gradient(circle, rgba(169, 143, 88, 0.2) 0%, transparent 70%);
    }

    .booth-making-call .booth-icon {
      background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0yNSAzNUg2NVY1NUgyNVYzNVoiIGZpbGw9IiMwMDAiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8yOF80NDgiIHgxPSI0NS41IiB5MT0iMCIgeDI9IjQ1LjUiIHkyPSI5MSIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkYzNjU4Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzBFRjhGOCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');
    }

    .booth-huddle-up {
      top: 3%;
      left: 54%;
      background: radial-gradient(circle, rgba(169, 143, 88, 0.2) 0%, transparent 70%);
    }

    .booth-huddle-up .booth-icon {
      background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxyZWN0IHg9IjIwIiB5PSIzNSIgd2lkdGg9IjUwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMDAwIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMjhfNDQ4IiB4MT0iNDUuNSIgeTE9IjAiIHgyPSI0NS41IiB5Mj0iOTEiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI0ZGMzY1OCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwRUY4RjgiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');
    }

    .booth-trust-issues {
      top: 52%;
      right: 23%;
      background: radial-gradient(circle, rgba(169, 143, 88, 0.2) 0%, transparent 70%);
    }

    .booth-trust-issues .booth-icon {
      background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0zNSAyNUg1NVY2NUgzNVYyNVoiIGZpbGw9IiMwMDAiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8yOF80NDgiIHgxPSI0NS41IiB5MT0iMCIgeDI9IjQ1LjUiIHkyPSI5MSIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkYzNjU4Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzBFRjhGOCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');
    }

    /* Booth Labels */
    .booth-label {
      position: absolute;
      bottom: -40px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(36, 36, 36, 0.9);
      color: white;
      padding: 8px 16px;
      border-radius: 15px;
      font-size: 12px;
      font-weight: 500;
      letter-spacing: 0.2em;
      text-align: center;
      white-space: nowrap;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      opacity: 0;
      transition: all 0.3s ease;
    }

    .booth-container:hover .booth-label {
      opacity: 1;
      bottom: -35px;
    }

    /* Lottie Animation Container */
    .lottie-decoration {
      position: absolute;
      width: 40px;
      height: 40px;
      pointer-events: none;
      opacity: 0.7;
    }

    .lottie-decoration.top-left {
      top: -10px;
      left: -10px;
    }

    .lottie-decoration.top-right {
      top: -10px;
      right: -10px;
    }

    .lottie-decoration.bottom-left {
      bottom: -10px;
      left: -10px;
    }

    .lottie-decoration.bottom-right {
      bottom: -10px;
      right: -10px;
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <!-- Interactive Map -->
    <div class="map-container">
      <!-- SVG Map Background -->
      <div class="map-background"></div>

      <!-- NBA DECODED Booth -->
      <a href="#nba-decoded" class="booth-container booth-nba-decoded" data-booth="nba-decoded">
        <div class="booth-icon">
          <!-- Lottie Decorations -->
          <lottie-player
            class="lottie-decoration top-left"
            src="https://assets3.lottiefiles.com/packages/lf20_jcikwtux.json"
            background="transparent"
            speed="1"
            loop
            autoplay>
          </lottie-player>
          <lottie-player
            class="lottie-decoration bottom-right"
            src="https://assets3.lottiefiles.com/packages/lf20_jcikwtux.json"
            background="transparent"
            speed="0.8"
            loop
            autoplay>
          </lottie-player>
        </div>
        <div class="booth-label">NBA DECODED</div>
      </a>

      <!-- MAKING THE CALL Booth -->
      <a href="#making-call" class="booth-container booth-making-call" data-booth="making-call">
        <div class="booth-icon">
          <!-- Lottie Decorations -->
          <lottie-player
            class="lottie-decoration top-right"
            src="https://assets3.lottiefiles.com/packages/lf20_jcikwtux.json"
            background="transparent"
            speed="1.2"
            loop
            autoplay>
          </lottie-player>
          <lottie-player
            class="lottie-decoration bottom-left"
            src="https://assets3.lottiefiles.com/packages/lf20_jcikwtux.json"
            background="transparent"
            speed="0.9"
            loop
            autoplay>
          </lottie-player>
        </div>
        <div class="booth-label">MAKING THE CALL</div>
      </a>

      <!-- HUDDLE UP Booth -->
      <a href="#huddle-up" class="booth-container booth-huddle-up" data-booth="huddle-up">
        <div class="booth-icon">
          <!-- Lottie Decorations -->
          <lottie-player
            class="lottie-decoration top-left"
            src="https://assets3.lottiefiles.com/packages/lf20_jcikwtux.json"
            background="transparent"
            speed="0.7"
            loop
            autoplay>
          </lottie-player>
          <lottie-player
            class="lottie-decoration top-right"
            src="https://assets3.lottiefiles.com/packages/lf20_jcikwtux.json"
            background="transparent"
            speed="1.1"
            loop
            autoplay>
          </lottie-player>
        </div>
        <div class="booth-label">HUDDLE UP</div>
      </a>

      <!-- TRUST ISSUES Booth -->
      <a href="#trust-issues" class="booth-container booth-trust-issues" data-booth="trust-issues">
        <div class="booth-icon">
          <!-- Lottie Decorations -->
          <lottie-player
            class="lottie-decoration bottom-left"
            src="https://assets3.lottiefiles.com/packages/lf20_jcikwtux.json"
            background="transparent"
            speed="1.3"
            loop
            autoplay>
          </lottie-player>
          <lottie-player
            class="lottie-decoration bottom-right"
            src="https://assets3.lottiefiles.com/packages/lf20_jcikwtux.json"
            background="transparent"
            speed="0.6"
            loop
            autoplay>
          </lottie-player>
        </div>
        <div class="booth-label">TRUST ISSUES</div>
      </a>
    </div>
    <!-- Right Sidebar with Accordions -->
    <div class="sidebar">
      <h2>Exhibit Details</h2>

      <!-- NBA DECODED Accordion -->
      <div class="accordion-item" id="accordion-nba-decoded">
        <div class="accordion-header">
          <h3>NBA DECODED</h3>
          <span class="accordion-toggle">+</span>
        </div>
        <div class="accordion-content">
          <p>Discover the secrets behind NBA analytics and data visualization. Learn how teams use advanced statistics to gain competitive advantages.</p>
          <ul>
            <li>Interactive data dashboards</li>
            <li>Player performance metrics</li>
            <li>Team strategy analysis</li>
            <li>Real-time game insights</li>
          </ul>
        </div>
      </div>

      <!-- MAKING THE CALL Accordion -->
      <div class="accordion-item" id="accordion-making-call">
        <div class="accordion-header">
          <h3>MAKING THE CALL</h3>
          <span class="accordion-toggle">+</span>
        </div>
        <div class="accordion-content">
          <p>Step into the shoes of an NBA referee and experience the split-second decisions that shape the game.</p>
          <ul>
            <li>Virtual reality referee training</li>
            <li>Rule interpretation challenges</li>
            <li>Instant replay scenarios</li>
            <li>Communication protocols</li>
          </ul>
        </div>
      </div>

      <!-- HUDDLE UP Accordion -->
      <div class="accordion-item" id="accordion-huddle-up">
        <div class="accordion-header">
          <h3>HUDDLE UP</h3>
          <span class="accordion-toggle">+</span>
        </div>
        <div class="accordion-content">
          <p>Experience team strategy and coaching decisions in high-pressure game situations.</p>
          <ul>
            <li>Strategic play calling</li>
            <li>Timeout management</li>
            <li>Player substitutions</li>
            <li>Game flow analysis</li>
          </ul>
        </div>
      </div>

      <!-- TRUST ISSUES Accordion -->
      <div class="accordion-item" id="accordion-trust-issues">
        <div class="accordion-header">
          <h3>TRUST ISSUES</h3>
          <span class="accordion-toggle">+</span>
        </div>
        <div class="accordion-content">
          <p>Explore the psychology of trust between players, coaches, and organizations in professional basketball.</p>
          <ul>
            <li>Team chemistry dynamics</li>
            <li>Leadership challenges</li>
            <li>Communication breakdowns</li>
            <li>Building team culture</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <style>
    /* Sidebar Styling */
    .sidebar {
      width: 400px;
      background: rgba(0, 0, 0, 0.9);
      border-radius: 20px;
      padding: 30px;
      border: 2px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      overflow-y: auto;
      max-height: calc(100vh - 40px);
    }

    .sidebar h2 {
      color: #0EF8F8;
      margin: 0 0 30px 0;
      font-size: 24px;
      font-weight: 600;
      text-align: center;
      text-transform: uppercase;
      letter-spacing: 0.1em;
    }

    /* Accordion Styling */
    .accordion-item {
      margin-bottom: 20px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .accordion-item.active {
      border-color: #0EF8F8;
      box-shadow: 0 0 20px rgba(14, 248, 248, 0.2);
    }

    .accordion-header {
      background: rgba(36, 36, 36, 0.8);
      padding: 20px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: all 0.3s ease;
    }

    .accordion-header:hover {
      background: rgba(36, 36, 36, 1);
    }

    .accordion-item.active .accordion-header {
      background: rgba(14, 248, 248, 0.1);
    }

    .accordion-header h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: white;
      letter-spacing: 0.1em;
    }

    .accordion-toggle {
      font-size: 24px;
      font-weight: bold;
      color: #0EF8F8;
      transition: transform 0.3s ease;
    }

    .accordion-item.active .accordion-toggle {
      transform: rotate(45deg);
    }

    .accordion-content {
      padding: 0 20px;
      max-height: 0;
      overflow: hidden;
      transition: all 0.3s ease;
      background: rgba(0, 0, 0, 0.5);
    }

    .accordion-item.active .accordion-content {
      padding: 20px;
      max-height: 300px;
    }

    .accordion-content p {
      margin: 0 0 15px 0;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
    }

    .accordion-content ul {
      margin: 0;
      padding-left: 20px;
      color: rgba(255, 255, 255, 0.7);
    }

    .accordion-content li {
      margin-bottom: 8px;
      line-height: 1.4;
    }

    /* Demo Controls */
    .demo-controls {
      position: fixed;
      top: 20px;
      left: 20px;
      background: rgba(0, 0, 0, 0.9);
      padding: 20px;
      border-radius: 15px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      z-index: 100;
    }

    .demo-controls h4 {
      margin: 0 0 15px 0;
      color: #0EF8F8;
      font-size: 14px;
    }

    .demo-controls button {
      display: block;
      width: 100%;
      margin-bottom: 10px;
      padding: 8px 12px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      color: white;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s ease;
    }

    .demo-controls button:hover {
      background: rgba(14, 248, 248, 0.2);
      border-color: #0EF8F8;
    }
  </style>

  <script>
    // Interactive Map Functionality
    class InteractiveMap {
      constructor() {
        this.booths = document.querySelectorAll('.booth-container');
        this.accordions = document.querySelectorAll('.accordion-item');
        this.init();
      }

      init() {
        // Add click handlers for booth containers
        this.booths.forEach(booth => {
          booth.addEventListener('click', (e) => {
            e.preventDefault();
            const boothId = booth.dataset.booth;
            this.openAccordion(boothId);
            this.setActiveBooths([boothId]);
          });
        });

        // Add click handlers for accordion headers
        this.accordions.forEach(accordion => {
          const header = accordion.querySelector('.accordion-header');
          header.addEventListener('click', () => {
            const isActive = accordion.classList.contains('active');

            // Close all accordions
            this.accordions.forEach(acc => acc.classList.remove('active'));

            // Open clicked accordion if it wasn't active
            if (!isActive) {
              accordion.classList.add('active');

              // Highlight corresponding booth
              const boothId = accordion.id.replace('accordion-', '');
              this.setActiveBooths([boothId]);
            } else {
              // Clear all booth highlights
              this.setActiveBooths([]);
            }
          });
        });
      }

      openAccordion(boothId) {
        // Close all accordions
        this.accordions.forEach(acc => acc.classList.remove('active'));

        // Open specific accordion
        const targetAccordion = document.getElementById(`accordion-${boothId}`);
        if (targetAccordion) {
          targetAccordion.classList.add('active');
        }
      }

      setActiveBooths(boothIds) {
        // Clear all booth states
        this.booths.forEach(booth => {
          booth.classList.remove('active', 'completed');
        });

        // Set active booths
        boothIds.forEach(boothId => {
          const booth = document.querySelector(`[data-booth="${boothId}"]`);
          if (booth) {
            booth.classList.add('active');
          }
        });
      }

      markBoothCompleted(boothId) {
        const booth = document.querySelector(`[data-booth="${boothId}"]`);
        if (booth) {
          booth.classList.remove('active');
          booth.classList.add('completed');
        }
      }

      // Demo functions for testing
      simulateCompletion() {
        const boothIds = ['nba-decoded', 'making-call', 'huddle-up', 'trust-issues'];
        let index = 0;

        const completeNext = () => {
          if (index < boothIds.length) {
            this.markBoothCompleted(boothIds[index]);
            index++;
            setTimeout(completeNext, 1500);
          }
        };

        completeNext();
      }

      resetBooths() {
        this.booths.forEach(booth => {
          booth.classList.remove('active', 'completed');
        });
        this.accordions.forEach(acc => acc.classList.remove('active'));
      }
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
      window.interactiveMap = new InteractiveMap();
    });
  </script>

  <!-- Demo Controls -->
  <div class="demo-controls">
    <h4>Demo Controls</h4>
    <button onclick="interactiveMap.simulateCompletion()">Simulate Completion</button>
    <button onclick="interactiveMap.resetBooths()">Reset Booths</button>
    <button onclick="interactiveMap.setActiveBooths(['nba-decoded'])">Activate NBA Decoded</button>
    <button onclick="interactiveMap.markBoothCompleted('making-call')">Complete Making Call</button>
  </div>

</body>
</html>