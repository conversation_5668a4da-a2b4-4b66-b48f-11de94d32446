/* Booth Icon Background Images - Modular CSS for Easy State Management */

/* Default State Icons */
.booth-nba-decoded .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0zMCAzMEg2MFY2MEgzMFYzMFoiIGZpbGw9IiMwMDAiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8yOF80NDgiIHgxPSI0NS41IiB5MT0iMCIgeDI9IjQ1LjUiIHkyPSI5MSIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkYzNjU4Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzBFRjhGOCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');
}

.booth-making-call .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0yNSAzNUg2NVY1NUgyNVYzNVoiIGZpbGw9IiMwMDAiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8yOF80NDgiIHgxPSI0NS41IiB5MT0iMCIgeDI9IjQ1LjUiIHkyPSI5MSIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkYzNjU4Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzBFRjhGOCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');
}

.booth-huddle-up .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxyZWN0IHg9IjIwIiB5PSIzNSIgd2lkdGg9IjUwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMDAwIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMjhfNDQ4IiB4MT0iNDUuNSIgeTE9IjAiIHgyPSI0NS41IiB5Mj0iOTEiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI0ZGMzY1OCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwRUY4RjgiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');
}

.booth-trust-issues .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0zNSAyNUg1NVY2NUgzNVYyNVoiIGZpbGw9IiMwMDAiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8yOF80NDgiIHgxPSI0NS41IiB5MT0iMCIgeDI9IjQ1LjUiIHkyPSI5MSIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkYzNjU4Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzBFRjhGOCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');
}

/* Completed State Icons (with checkmarks) */
.booth-nba-decoded.completed .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0zMCAzMEg2MFY2MEgzMFYzMFoiIGZpbGw9IiMwMDAiLz4KPHBhdGggZD0iTTM4IDQ1TDQzIDUwTDUzIDQwIiBzdHJva2U9IiMwRUY4RjgiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMjhfNDQ4IiB4MT0iNDUuNSIgeTE9IjAiIHgyPSI0NS41IiB5Mj0iOTEiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI0ZGMzY1OCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwRUY4RjgiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');
}

.booth-making-call.completed .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0yNSAzNUg2NVY1NUgyNVYzNVoiIGZpbGw9IiMwMDAiLz4KPHBhdGggZD0iTTM4IDQ1TDQzIDUwTDUzIDQwIiBzdHJva2U9IiMwRUY4RjgiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMjhfNDQ4IiB4MT0iNDUuNSIgeTE9IjAiIHgyPSI0NS41IiB5Mj0iOTEiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI0ZGMzY1OCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwRUY4RjgiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');
}

.booth-huddle-up.completed .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxyZWN0IHg9IjIwIiB5PSIzNSIgd2lkdGg9IjUwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMDAwIi8+CjxwYXRoIGQ9Ik0zOCA0NUw0MyA1MEw1MyA0MCIgc3Ryb2tlPSIjMEVGOEY4IiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzI4XzQ0OCIgeDE9IjQ1LjUiIHkxPSIwIiB4Mj0iNDUuNSIgeTI9IjkxIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiNGRjM2NTgiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjMEVGOEY4Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+Cg==');
}

.booth-trust-issues.completed .booth-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTEiIGhlaWdodD0iOTEiIHZpZXdCb3g9IjAgMCA5MSA5MSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ1LjUiIHI9IjQ1LjUiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yOF80NDgpIi8+CjxwYXRoIGQ9Ik0zNSAyNUg1NVY2NUgzNVYyNVoiIGZpbGw9IiMwMDAiLz4KPHBhdGggZD0iTTM4IDQ1TDQzIDUwTDUzIDQwIiBzdHJva2U9IiMwRUY4RjgiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMjhfNDQ4IiB4MT0iNDUuNSIgeTE9IjAiIHgyPSI0NS41IiB5Mj0iOTEiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI0ZGMzY1OCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwRUY4RjgiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');
}

/* Active State Enhancements */
.booth-container.active .booth-icon {
  animation: activePulse 2s ease-in-out infinite;
}

.booth-container.completed .booth-icon {
  animation: completedGlow 3s ease-in-out infinite;
}

@keyframes activePulse {
  0%, 100% { 
    transform: scale(1);
    filter: drop-shadow(0 0 15px rgba(255, 54, 88, 0.6));
  }
  50% { 
    transform: scale(1.05);
    filter: drop-shadow(0 0 25px rgba(255, 54, 88, 0.9));
  }
}

@keyframes completedGlow {
  0%, 100% { 
    filter: drop-shadow(0 0 15px rgba(14, 248, 248, 0.6));
  }
  50% { 
    filter: drop-shadow(0 0 25px rgba(14, 248, 248, 0.9));
  }
}

/* Usage Instructions:
 * 
 * To change booth states, simply add/remove CSS classes:
 * 
 * Default state: <a class="booth-container booth-nba-decoded">
 * Active state:  <a class="booth-container booth-nba-decoded active">
 * Complete state: <a class="booth-container booth-nba-decoded completed">
 * 
 * The background images will automatically switch based on the class.
 * You can easily replace the base64 encoded SVGs with your own custom icons.
 */
