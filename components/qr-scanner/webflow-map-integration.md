# Interactive Exhibit Map - Webflow Integration Guide

## Overview
This interactive map system provides clickable booth containers positioned absolutely over an SVG map background, with accordion-style sidebar details and Lottie animations.

## File Structure
```
components/qr-scanner/
├── interactive-map.html          # Complete demo with all functionality
├── booth-icons.css              # Modular icon backgrounds for easy state management
└── webflow-map-integration.md   # This integration guide
```

## Webflow Implementation Steps

### 1. HTML Structure in Webflow

Create this structure in your Webflow designer:

```html
<!-- Map Container -->
<div class="map-container">
  <!-- SVG Background -->
  <div class="map-background"></div>
  
  <!-- NBA DECODED Booth -->
  <a href="#nba-decoded" class="booth-container booth-nba-decoded" data-booth="nba-decoded">
    <div class="booth-icon">
      <!-- Lottie Player Elements -->
      <div class="lottie-decoration top-left" data-lottie="sparkle"></div>
      <div class="lottie-decoration bottom-right" data-lottie="sparkle"></div>
    </div>
    <div class="booth-label">NBA DECODED</div>
  </a>
  
  <!-- Repeat for other booths: making-call, huddle-up, trust-issues -->
</div>

<!-- Sidebar -->
<div class="sidebar">
  <h2>Exhibit Details</h2>
  
  <!-- Accordion Items -->
  <div class="accordion-item" id="accordion-nba-decoded">
    <div class="accordion-header">
      <h3>NBA DECODED</h3>
      <span class="accordion-toggle">+</span>
    </div>
    <div class="accordion-content">
      <p>Content here...</p>
    </div>
  </div>
  
  <!-- Repeat for other accordions -->
</div>
```

### 2. CSS Implementation

#### Option A: Embed in Page Settings
Copy the CSS from `interactive-map.html` into your page's custom code section.

#### Option B: External Stylesheet
Upload `booth-icons.css` as a hosted file and link it:
```html
<link rel="stylesheet" href="/path/to/booth-icons.css">
```

### 3. Key CSS Classes for Webflow

```css
/* Essential positioning classes */
.booth-nba-decoded { top: 40%; right: 8%; }
.booth-making-call { top: 6%; right: 13%; }
.booth-huddle-up { top: 3%; left: 54%; }
.booth-trust-issues { top: 52%; right: 23%; }

/* State management classes */
.booth-container.active { /* Red pulsing effect */ }
.booth-container.completed { /* Cyan glow effect */ }
```

### 4. JavaScript Integration

Add this to your page's custom code (before closing `</body>`):

```html
<script>
// Interactive Map Class
class InteractiveMap {
  constructor() {
    this.booths = document.querySelectorAll('.booth-container');
    this.accordions = document.querySelectorAll('.accordion-item');
    this.init();
  }
  
  init() {
    // Booth click handlers
    this.booths.forEach(booth => {
      booth.addEventListener('click', (e) => {
        e.preventDefault();
        const boothId = booth.dataset.booth;
        this.openAccordion(boothId);
        this.setActiveBooths([boothId]);
      });
    });
    
    // Accordion click handlers
    this.accordions.forEach(accordion => {
      const header = accordion.querySelector('.accordion-header');
      header.addEventListener('click', () => {
        const isActive = accordion.classList.contains('active');
        this.accordions.forEach(acc => acc.classList.remove('active'));
        
        if (!isActive) {
          accordion.classList.add('active');
          const boothId = accordion.id.replace('accordion-', '');
          this.setActiveBooths([boothId]);
        } else {
          this.setActiveBooths([]);
        }
      });
    });
  }
  
  openAccordion(boothId) {
    this.accordions.forEach(acc => acc.classList.remove('active'));
    const target = document.getElementById(`accordion-${boothId}`);
    if (target) target.classList.add('active');
  }
  
  setActiveBooths(boothIds) {
    this.booths.forEach(booth => {
      booth.classList.remove('active', 'completed');
    });
    
    boothIds.forEach(boothId => {
      const booth = document.querySelector(`[data-booth="${boothId}"]`);
      if (booth) booth.classList.add('active');
    });
  }
  
  markBoothCompleted(boothId) {
    const booth = document.querySelector(`[data-booth="${boothId}"]`);
    if (booth) {
      booth.classList.remove('active');
      booth.classList.add('completed');
    }
  }
}

// Initialize when DOM loads
document.addEventListener('DOMContentLoaded', () => {
  window.interactiveMap = new InteractiveMap();
});
</script>
```

### 5. Lottie Animation Setup

#### Option A: Lottie Web Player
```html
<script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>

<!-- In your booth containers -->
<lottie-player 
  class="lottie-decoration top-left"
  src="YOUR_LOTTIE_FILE_URL.json"
  background="transparent"
  speed="1"
  loop
  autoplay>
</lottie-player>
```

#### Option B: Custom Lottie Integration
```javascript
// Initialize Lottie animations
document.querySelectorAll('[data-lottie]').forEach(element => {
  // Your custom Lottie initialization code
});
```

### 6. SVG Map Background

Replace the placeholder SVG with your actual map:

```css
.map-background {
  background-image: url('YOUR_MAP_SVG_URL');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
```

### 7. State Management API

Use these methods to control booth states:

```javascript
// Mark booth as completed
interactiveMap.markBoothCompleted('nba-decoded');

// Set active booth
interactiveMap.setActiveBooths(['making-call']);

// Open specific accordion
interactiveMap.openAccordion('huddle-up');

// Reset all booths
interactiveMap.setActiveBooths([]);
```

### 8. Responsive Considerations

The booth positions are percentage-based and should scale with the container. Test on your target devices (Surface Go 4, iPad 11-inch) and adjust positions if needed.

### 9. Custom Icon Integration

To use your own booth icons:

1. Create SVG icons for default and completed states
2. Convert to base64 or host as files
3. Update the CSS in `booth-icons.css`
4. Replace the background-image URLs

### 10. Performance Optimization

- Preload Lottie animations
- Optimize SVG file sizes
- Use CSS transforms for animations (GPU accelerated)
- Consider lazy loading for non-critical animations

## Testing Checklist

- [ ] Booth containers are clickable
- [ ] Accordions open/close correctly
- [ ] Booth states change visually (active/completed)
- [ ] Lottie animations play smoothly
- [ ] Map scales properly on target devices
- [ ] Touch interactions work on tablets
- [ ] Performance is smooth with all animations running

## Customization Options

- Adjust booth positions in CSS
- Replace Lottie animation files
- Modify color scheme (currently uses #0EF8F8 and #FF3658)
- Add more booth states (in-progress, locked, etc.)
- Customize accordion content and styling
- Add sound effects for interactions
