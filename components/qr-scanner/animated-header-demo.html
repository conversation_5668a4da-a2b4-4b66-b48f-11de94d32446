<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Animated Scanner Header Demo</title>
  
  <!-- Include the HTML5 QR Code library -->
  <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
  
  <!-- Include the QR Scanner styles -->
  <link rel="stylesheet" href="qr-scanner-styles.css">
  
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      color: white;
      min-height: 100vh;
    }
    
    .demo-container {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
    }
    
    .demo-controls {
      margin: 30px 0;
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .demo-button {
      padding: 12px 24px;
      background: linear-gradient(135deg, #0EF8F8 0%, #FF3658 100%);
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      font-size: 16px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(14, 248, 248, 0.3);
    }
    
    .demo-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(14, 248, 248, 0.4);
    }
    
    .demo-button:active {
      transform: translateY(0);
    }
    
    .info-box {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 20px;
      margin: 20px 0;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .qr-scanner-container {
      margin-top: 30px;
      height: 400px;
    }
    
    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-left: 10px;
      background: #666;
      transition: all 0.3s ease;
    }
    
    .status-indicator.active {
      background: #0EF8F8;
      box-shadow: 0 0 10px #0EF8F8;
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <h1>🎯 Animated Scanner Header Demo</h1>
    
    <div class="info-box">
      <h2>Scanner Status Header</h2>
      <p>Watch the header below transform when the scanner is activated!</p>
      <p>Status: <span id="status-text">Ready</span> <span class="status-indicator" id="status-dot"></span></p>
    </div>
    
    <!-- This is your H3 header with the .activated class -->
    <h3 class="activated">SCANNER READY</h3>
    
    <div class="demo-controls">
      <button class="demo-button" onclick="activateScanner()">🚀 Activate Scanner</button>
      <button class="demo-button" onclick="deactivateScanner()">⏹️ Deactivate Scanner</button>
      <button class="demo-button" onclick="toggleScanner()">🔄 Toggle Scanner</button>
    </div>
    
    <div class="info-box">
      <h3>✨ Animation Features:</h3>
      <ul style="text-align: left; max-width: 500px; margin: 0 auto;">
        <li><strong>Gradient Flow:</strong> Dynamic color transitions</li>
        <li><strong>Pulsing Effect:</strong> Subtle scale animation</li>
        <li><strong>Glowing Border:</strong> Animated border scan effect</li>
        <li><strong>Text Glow:</strong> Alternating text shadow intensity</li>
        <li><strong>Scanning Dot:</strong> Pulsing indicator on the right</li>
        <li><strong>Sci-Fi Colors:</strong> Your custom #0EF8F8 and #FF3658</li>
      </ul>
    </div>
    
    <!-- QR Scanner Container (optional - for full demo) -->
    <div class="qr-scanner-container" style="display: none;">
      <div class="qr-scanner-video" id="qr-video"></div>
    </div>
  </div>

  <!-- Include the QR Scanner script -->
  <script src="qr-scanner-simple.js"></script>
  
  <script>
    let isActive = false;
    
    function activateScanner() {
      const header = document.querySelector('h3.activated');
      const statusText = document.getElementById('status-text');
      const statusDot = document.getElementById('status-dot');
      
      if (header) {
        header.textContent = 'SCANNER ACTIVATED';
        header.classList.add('scanner-active');
        statusText.textContent = 'Scanning...';
        statusDot.classList.add('active');
        isActive = true;
        console.log('🎯 Demo: Scanner activated');
      }
    }
    
    function deactivateScanner() {
      const header = document.querySelector('h3.activated');
      const statusText = document.getElementById('status-text');
      const statusDot = document.getElementById('status-dot');
      
      if (header) {
        header.textContent = 'SCANNER READY';
        header.classList.remove('scanner-active');
        statusText.textContent = 'Ready';
        statusDot.classList.remove('active');
        isActive = false;
        console.log('⏸️ Demo: Scanner deactivated');
      }
    }
    
    function toggleScanner() {
      if (isActive) {
        deactivateScanner();
      } else {
        activateScanner();
      }
    }
    
    // Auto-demo: activate after 2 seconds
    setTimeout(() => {
      activateScanner();
      
      // Then toggle every 4 seconds for demo
      setInterval(() => {
        toggleScanner();
      }, 4000);
    }, 2000);
  </script>
</body>
</html>
