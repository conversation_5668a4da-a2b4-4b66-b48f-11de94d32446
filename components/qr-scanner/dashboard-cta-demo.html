<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard Scanner CTA Pulse Demo</title>
  
  <!-- Include the QR Scanner styles -->
  <link rel="stylesheet" href="qr-scanner-styles.css">
  
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      color: white;
      min-height: 100vh;
    }
    
    .demo-container {
      max-width: 900px;
      margin: 0 auto;
      text-align: center;
    }
    
    .dashboard-mockup {
      background: rgba(0, 0, 0, 0.8);
      border-radius: 20px;
      padding: 40px;
      margin: 30px 0;
      position: relative;
      min-height: 400px;
      border: 2px solid rgba(255, 255, 255, 0.1);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 30px;
    }
    
    .dashboard-title {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 20px;
      color: #0EF8F8;
    }
    
    /* Mock dashboard CTA button */
    .activate-scanner-container {
      text-decoration: none;
      color: inherit;
      border-radius: 16px;
      overflow: hidden;
      display: inline-block;
      cursor: pointer;
    }
    
    .open-scanner {
      background: linear-gradient(135deg, #0EF8F8 0%, #FF3658 100%);
      color: white;
      padding: 20px 40px;
      border-radius: 16px;
      font-weight: 700;
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 15px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 20px rgba(14, 248, 248, 0.3);
      min-width: 200px;
      justify-content: center;
    }
    
    .activate-scanner-container:hover .open-scanner {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(14, 248, 248, 0.4);
    }
    
    /* Camera icon styling */
    .icon_camera {
      width: 24px;
      height: 24px;
      background: white;
      border-radius: 4px;
      position: relative;
      transition: all 0.3s ease;
      flex-shrink: 0;
    }
    
    /* Create a simple camera icon with CSS */
    .icon_camera::before {
      content: '';
      position: absolute;
      top: -3px;
      left: 50%;
      transform: translateX(-50%);
      width: 6px;
      height: 3px;
      background: white;
      border-radius: 1px;
    }
    
    .icon_camera::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 10px;
      height: 10px;
      border: 2px solid #333;
      border-radius: 50%;
      background: transparent;
    }
    
    .demo-controls {
      margin: 30px 0;
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .demo-button {
      padding: 12px 24px;
      background: linear-gradient(135deg, #0EF8F8 0%, #FF3658 100%);
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      font-size: 16px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(14, 248, 248, 0.3);
    }
    
    .demo-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(14, 248, 248, 0.4);
    }
    
    .demo-button:active {
      transform: translateY(0);
    }
    
    .info-box {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 20px;
      margin: 20px 0;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-left: 10px;
      background: #666;
      transition: all 0.3s ease;
    }
    
    .status-indicator.active {
      background: #0EF8F8;
      box-shadow: 0 0 10px #0EF8F8;
      animation: statusPulse 2.5s ease-in-out infinite;
    }
    
    @keyframes statusPulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
    
    .dashboard-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
      margin: 20px 0;
      width: 100%;
      max-width: 600px;
    }
    
    .stat-card {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .stat-number {
      font-size: 24px;
      font-weight: 700;
      color: #0EF8F8;
    }
    
    .stat-label {
      font-size: 14px;
      color: #ccc;
      margin-top: 5px;
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <h1>🎯 Dashboard Scanner CTA Pulse Demo</h1>
    
    <div class="info-box">
      <h2>Scanner CTA Button</h2>
      <p>Watch the "Open Scanner" button pulse and glow when activated!</p>
      <p>Status: <span id="status-text">Normal</span> <span class="status-indicator" id="status-dot"></span></p>
    </div>
    
    <div class="dashboard-mockup">
      <div class="dashboard-title">🏀 NBA Atlas Dashboard</div>
      
      <div class="dashboard-stats">
        <div class="stat-card">
          <div class="stat-number">42</div>
          <div class="stat-label">Scans Today</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">156</div>
          <div class="stat-label">Total Users</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">98%</div>
          <div class="stat-label">Success Rate</div>
        </div>
      </div>
      
      <!-- This is your scanner CTA button -->
      <a href="#" class="activate-scanner-container" id="scanner-cta">
        <div class="open-scanner">
          <img class="icon_camera" />
          Open Scanner
        </div>
      </a>
    </div>
    
    <div class="demo-controls">
      <button class="demo-button" onclick="activateCTA()">✨ Activate CTA Pulse</button>
      <button class="demo-button" onclick="deactivateCTA()">⏹️ Deactivate Pulse</button>
      <button class="demo-button" onclick="toggleCTA()">🔄 Toggle Pulse</button>
    </div>
    
    <div class="info-box">
      <h3>✨ CTA Animation Features:</h3>
      <ul style="text-align: left; max-width: 500px; margin: 0 auto;">
        <li><strong>Gentle Pulse:</strong> 2.5-second breathing effect (1.0 → 1.03 → 1.0)</li>
        <li><strong>Button Glow:</strong> Cyan glow that intensifies during pulse</li>
        <li><strong>Icon Glow:</strong> Red camera icon glow effect</li>
        <li><strong>Flowing Border:</strong> Animated gradient border</li>
        <li><strong>Pulsing Ring:</strong> Subtle outer ring effect</li>
        <li><strong>Smooth Timing:</strong> All effects synchronized perfectly</li>
      </ul>
    </div>
    
    <div class="info-box">
      <h3>🎯 Webflow Implementation:</h3>
      <p><strong>Step 1:</strong> Add this CSS to your Webflow custom code</p>
      <p><strong>Step 2:</strong> Add class "scanner-cta-active" to your .activate-scanner-container when you want the pulse</p>
      <p><strong>Step 3:</strong> Remove "scanner-cta-active" class to stop the pulse</p>
      
      <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin: 15px 0; text-align: left;">
        <code style="color: #0EF8F8;">
          // JavaScript to control the effect<br>
          document.querySelector('.activate-scanner-container').classList.add('scanner-cta-active');<br>
          document.querySelector('.activate-scanner-container').classList.remove('scanner-cta-active');
        </code>
      </div>
    </div>
  </div>

  <script>
    let isActive = false;
    
    function activateCTA() {
      const ctaButton = document.getElementById('scanner-cta');
      const statusText = document.getElementById('status-text');
      const statusDot = document.getElementById('status-dot');
      
      ctaButton.classList.add('scanner-cta-active');
      statusText.textContent = 'Pulsing...';
      statusDot.classList.add('active');
      isActive = true;
      
      console.log('✨ CTA pulse activated');
    }
    
    function deactivateCTA() {
      const ctaButton = document.getElementById('scanner-cta');
      const statusText = document.getElementById('status-text');
      const statusDot = document.getElementById('status-dot');
      
      ctaButton.classList.remove('scanner-cta-active');
      statusText.textContent = 'Normal';
      statusDot.classList.remove('active');
      isActive = false;
      
      console.log('⏹️ CTA pulse deactivated');
    }
    
    function toggleCTA() {
      if (isActive) {
        deactivateCTA();
      } else {
        activateCTA();
      }
    }
    
    // Auto-demo: activate after 2 seconds
    setTimeout(() => {
      activateCTA();
      
      // Then toggle every 6 seconds for demo
      setInterval(() => {
        toggleCTA();
      }, 6000);
    }, 2000);
    
    // Prevent default link behavior
    document.getElementById('scanner-cta').addEventListener('click', (e) => {
      e.preventDefault();
      toggleCTA();
    });
  </script>
</body>
</html>
