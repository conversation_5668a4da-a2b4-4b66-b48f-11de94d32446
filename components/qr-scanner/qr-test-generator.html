<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QR Code Test Generator</title>
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f7;
    }
    
    .container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #1d1d1f;
      text-align: center;
      margin-bottom: 30px;
    }
    
    .test-section {
      margin-bottom: 40px;
      padding: 20px;
      border: 2px solid #e5e5e7;
      border-radius: 8px;
    }
    
    .test-section h3 {
      color: #007AFF;
      margin-top: 0;
    }
    
    button {
      background-color: #007AFF;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      margin: 5px;
      transition: background-color 0.2s;
    }
    
    button:hover {
      background-color: #0056CC;
    }
    
    .qr-display {
      text-align: center;
      margin: 20px 0;
    }
    
    .qr-data {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      font-family: monospace;
      font-size: 14px;
      margin: 10px 0;
      word-break: break-all;
    }
    
    canvas {
      border: 2px solid #e5e5e7;
      border-radius: 8px;
    }
    
    .instructions {
      background-color: #e3f2fd;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🏀 QR Code Test Generator</h1>
    
    <div class="instructions">
      <strong>Instructions:</strong> Generate different types of QR codes to test your scanner. 
      Use your tablet to scan these codes and check the console logs for debugging information.
    </div>

    <!-- Simple Text QR Code -->
    <div class="test-section">
      <h3>1. Simple Text QR Code</h3>
      <p>Basic text that should display as "QR Code: [text]"</p>
      <button onclick="generateSimpleText()">Generate Simple Text</button>
      <div class="qr-display" id="simple-display"></div>
    </div>

    <!-- JSON QR Code -->
    <div class="test-section">
      <h3>2. JSON Format QR Code</h3>
      <p>Structured JSON data for different booth types</p>
      <button onclick="generateScore()">Generate Score Data</button>
      <button onclick="generateQuiz()">Generate Quiz Data</button>
      <button onclick="generateAR()">Generate AR Data</button>
      <div class="qr-display" id="json-display"></div>
    </div>

    <!-- URL QR Code -->
    <div class="test-section">
      <h3>3. URL Format QR Code</h3>
      <p>URL with parameters</p>
      <button onclick="generateURL()">Generate URL</button>
      <div class="qr-display" id="url-display"></div>
    </div>

    <!-- Custom Format QR Code -->
    <div class="test-section">
      <h3>4. Custom Format QR Code</h3>
      <p>Custom format: type:id:data</p>
      <button onclick="generateCustom()">Generate Custom Format</button>
      <div class="qr-display" id="custom-display"></div>
    </div>

    <!-- Edge Cases -->
    <div class="test-section">
      <h3>5. Edge Cases</h3>
      <p>Test problematic scenarios</p>
      <button onclick="generateEmpty()">Generate Empty String</button>
      <button onclick="generateSpecialChars()">Generate Special Characters</button>
      <button onclick="generateLongText()">Generate Long Text</button>
      <div class="qr-display" id="edge-display"></div>
    </div>
  </div>

  <script>
    // Generate QR code and display
    async function generateQR(data, containerId) {
      const container = document.getElementById(containerId);
      
      // Clear previous content
      container.innerHTML = '';
      
      // Show the data being encoded
      const dataDiv = document.createElement('div');
      dataDiv.className = 'qr-data';
      dataDiv.textContent = `Data: ${data}`;
      container.appendChild(dataDiv);
      
      // Generate QR code
      try {
        const canvas = document.createElement('canvas');
        await QRCode.toCanvas(canvas, data, {
          width: 256,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });
        container.appendChild(canvas);
      } catch (error) {
        console.error('Error generating QR code:', error);
        const errorDiv = document.createElement('div');
        errorDiv.textContent = `Error: ${error.message}`;
        errorDiv.style.color = 'red';
        container.appendChild(errorDiv);
      }
    }

    // Test functions
    function generateSimpleText() {
      generateQR('Hello NBA Atlas 2025!', 'simple-display');
    }

    function generateScore() {
      const scoreData = {
        type: 'score',
        data: {
          score: 95,
          gameId: 'game_123',
          boothId: 'referee_01',
          timestamp: new Date().toISOString()
        }
      };
      generateQR(JSON.stringify(scoreData), 'json-display');
    }

    function generateQuiz() {
      const quizData = {
        type: 'quiz',
        data: {
          quizId: 'quiz_456',
          title: 'NBA Rules Quiz',
          difficulty: 'medium'
        }
      };
      generateQR(JSON.stringify(quizData), 'json-display');
    }

    function generateAR() {
      const arData = {
        type: 'ar',
        data: {
          arId: 'ar_789',
          name: 'Basketball Training AR',
          scene: 'court_practice'
        }
      };
      generateQR(JSON.stringify(arData), 'json-display');
    }

    function generateURL() {
      const url = 'https://nba-atlas.com/scan?type=score&id=game_123&data=' + 
                  encodeURIComponent(JSON.stringify({score: 88, team: 'Lakers'}));
      generateQR(url, 'url-display');
    }

    function generateCustom() {
      const customData = 'score:game_456:{"points":92,"quarter":4}';
      generateQR(customData, 'custom-display');
    }

    function generateEmpty() {
      generateQR('', 'edge-display');
    }

    function generateSpecialChars() {
      generateQR('Special chars: !@#$%^&*()_+{}|:"<>?[]\\;\',./', 'edge-display');
    }

    function generateLongText() {
      const longText = 'This is a very long text string that contains a lot of information and should test how the QR scanner handles longer content. '.repeat(5);
      generateQR(longText, 'edge-display');
    }

    // Generate a simple test QR on page load
    window.addEventListener('load', () => {
      generateSimpleText();
    });
  </script>
</body>
</html>
