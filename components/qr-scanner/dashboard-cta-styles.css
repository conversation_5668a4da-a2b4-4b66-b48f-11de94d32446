/* Dashboard Scanner CTA Pulse Effect - Standalone Code Embed */
/* Simple camera icon pulse effect similar to scanner page */

/* Base styling for the CTA container */
.activate-scanner-container {
  position: relative;
  transition: all 0.3s ease;
  display: inline-block;
}

/* Camera icon pulse effect - Auto-active with 2s delay */
.activate-scanner-container .icon_camera {
  position: relative;
  transition: all 0.3s ease;
  /* Auto-start pulse animation with 2 second delay */
  animation: cameraIconPulse 2s ease-in-out infinite;
  animation-delay: 2s;
}

/* Subtle pulsing ring around camera icon only */
.activate-scanner-container .icon_camera::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid rgba(255, 54, 88, 0.4);
  border-radius: 50%;
  pointer-events: none;
  animation: iconRingPulse 2s ease-in-out infinite;
  animation-delay: 2s;
}

/* Keyframe animations for camera icon pulse */
@keyframes cameraIconPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 8px rgba(255, 54, 88, 0.6));
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 0 16px rgba(255, 54, 88, 0.9));
    opacity: 0.9;
  }
}

@keyframes iconRingPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}
