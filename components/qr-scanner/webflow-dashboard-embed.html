<!-- Dashboard Scanner CTA Pulse Effect - Webflow Code Embed -->
<!-- Add this to your Dashboard page's custom code (Before </body> tag) -->

<style>
/* Dashboard Scanner CTA Pulse Effect */

/* Base styling for the CTA container */
.activate-scanner-container {
  transition: all 0.3s ease;
}

/* Camera icon pulse effect - Auto-active with 2s delay */
.activate-scanner-container .icon_camera {
  position: relative;
  transition: all 0.3s ease;
  /* Auto-start pulse animation with 2 second delay */
  animation: cameraIconPulse 2s ease-in-out infinite;
  animation-delay: 2s;
}

/* Subtle pulsing ring around camera icon only */
.activate-scanner-container .icon_camera::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid rgba(255, 54, 88, 0.4);
  border-radius: 50%;
  pointer-events: none;
  animation: iconRingPulse 2s ease-in-out infinite;
  animation-delay: 2s;
}



/* Keyframe animations for camera icon pulse */
@keyframes cameraIconPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 8px rgba(255, 54, 88, 0.6));
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 0 16px rgba(255, 54, 88, 0.9));
    opacity: 0.9;
  }
}

@keyframes iconRingPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}
</style>

<script>
// Dashboard Scanner CTA Control Functions

// Function to activate the pulse effect
function activateScannerCTA() {
  const ctaElement = document.querySelector('.activate-scanner-container');
  if (ctaElement) {
    ctaElement.classList.add('scanner-cta-active');
    console.log('✨ Scanner CTA pulse activated');
  }
}

// Function to deactivate the pulse effect
function deactivateScannerCTA() {
  const ctaElement = document.querySelector('.activate-scanner-container');
  if (ctaElement) {
    ctaElement.classList.remove('scanner-cta-active');
    console.log('⏹️ Scanner CTA pulse deactivated');
  }
}

// Function to toggle the pulse effect
function toggleScannerCTA() {
  const ctaElement = document.querySelector('.activate-scanner-container');
  if (ctaElement) {
    if (ctaElement.classList.contains('scanner-cta-active')) {
      deactivateScannerCTA();
    } else {
      activateScannerCTA();
    }
  }
}

// Auto-activate example (remove if not needed)
// Uncomment the lines below to automatically start the pulse effect
/*
document.addEventListener('DOMContentLoaded', function() {
  // Wait 2 seconds then activate the pulse
  setTimeout(() => {
    activateScannerCTA();
  }, 2000);
});
*/

// Example: Activate pulse when certain conditions are met
// You can customize this logic based on your needs
/*
document.addEventListener('DOMContentLoaded', function() {
  // Example: Activate pulse if user hasn't used scanner recently
  const lastScanTime = localStorage.getItem('lastScanTime');
  const now = Date.now();
  const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds
  
  if (!lastScanTime || (now - parseInt(lastScanTime)) > oneHour) {
    // User hasn't scanned in over an hour, activate pulse
    setTimeout(() => {
      activateScannerCTA();
    }, 3000);
  }
});
*/
</script>

<!-- 
USAGE INSTRUCTIONS:

1. Copy this entire code block
2. In Webflow, go to your Dashboard page settings
3. Paste this code in the "Before </body> tag" section
4. Publish your site

Your HTML structure should be:
<a class="activate-scanner-container">
  <div class="open-scanner">
    <img class="icon_camera" src="your-icon.svg" />
    Open Scanner
  </div>
</a>

To control the effect with JavaScript:
- activateScannerCTA()   // Start pulsing
- deactivateScannerCTA() // Stop pulsing  
- toggleScannerCTA()     // Toggle on/off

Customization options:
- Change pulse speed: Modify "2.5s" in the animations
- Change colors: Modify the rgba() values
- Change intensity: Adjust the scale values (1.03) and glow sizes
-->
