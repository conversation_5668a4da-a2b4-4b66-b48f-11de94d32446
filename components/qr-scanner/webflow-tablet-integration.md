# QR Scanner - Tablet Optimization for Webflow

## Overview
This guide provides optimized QR scanner integration for Microsoft Surface Go 4 and iPad 11-inch tablets in landscape mode. The optimizations focus on:

- **Horizontal space utilization** - Camera viewport fills available landscape space
- **Touch-friendly buttons** - Larger buttons with proper text and icons
- **Responsive design** - Specific optimizations for target tablet resolutions
- **Performance** - Optimized QR box sizing for landscape scanning

## Target Devices
- **Microsoft Surface Go 4**: 10.5" display, 1920x1280 resolution (landscape)
- **iPad 11-inch (A16)**: 11" display, 2360x1640 resolution (landscape)

## Quick Integration Steps

### 1. Add Required Libraries
Add this to your Webflow page's `<head>` section or before closing `</body>`:

```html
<!-- HTML5 QR Code Library -->
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
```

### 2. Add CSS Styles
Create a new embed element and add the optimized CSS:

```html
<style>
/* Copy the entire contents of qr-scanner-styles.css here */
/* The file includes tablet-specific optimizations for landscape mode */
</style>
```

### 3. Add HTML Structure
Create the QR scanner container with proper button text:

```html
<div class="qr-scanner-container" data-scan-mode="continuous" data-booth-type="referee">
  <!-- Video element for camera feed -->
  <div class="qr-scanner-video" id="qr-video"></div>
  
  <!-- Buttons with proper text content -->
  <div class="qr-scanner-buttons">
    <button class="qr-scanner-start-button">Start Scanning</button>
    <button class="qr-scanner-stop-button">Stop Scanning</button>
    <button class="qr-scanner-switch-camera">Switch Camera</button>
    <button class="qr-scanner-toggle-flash">Toggle Flash</button>
  </div>
  
  <!-- Status displays -->
  <div class="qr-scanner-result"></div>
  <div class="qr-scanner-error"></div>
  <div class="qr-scanner-status"></div>
  <div class="qr-scanner-offline-indicator">Offline Mode</div>
</div>
```

### 4. Add JavaScript
Add the optimized JavaScript in another embed element:

```html
<script>
/* Copy the entire contents of qr-scanner_webflow-embed-modified.js here */
/* The file includes landscape optimizations and button text fallbacks */
</script>
```

## Key Optimizations

### 1. Landscape Layout Optimization
- **Full viewport usage**: Scanner container uses `height: 100vh` in landscape
- **Flexible video sizing**: Camera feed adapts to available horizontal space
- **Optimized QR box**: Larger scanning area for landscape orientation

### 2. Button Enhancements
- **Visual indicators**: Buttons include emoji icons (▶, ⏹, 🔄, 💡)
- **Touch-friendly sizing**: Minimum 48px height for tablet interaction
- **Automatic text**: JavaScript adds button text if missing from HTML
- **Improved styling**: Modern design with shadows and hover effects

### 3. Device-Specific Media Queries

#### Microsoft Surface Go 4 (1920x1280)
```css
@media screen and (min-width: 1200px) and (max-width: 1920px) 
       and (min-height: 1000px) and (max-height: 1280px) {
  .qr-scanner-video {
    min-height: 600px;
    max-height: calc(100vh - 140px);
  }
  
  .qr-scanner-start-button,
  .qr-scanner-stop-button,
  .qr-scanner-switch-camera,
  .qr-scanner-toggle-flash {
    min-width: 140px;
    padding: 14px 24px;
    font-size: 18px;
  }
}
```

#### iPad 11-inch (2360x1640)
```css
@media screen and (min-width: 1600px) and (max-width: 2360px) 
       and (min-height: 1200px) and (max-height: 1640px) {
  .qr-scanner-video {
    min-height: 700px;
    max-height: calc(100vh - 160px);
  }
  
  .qr-scanner-start-button,
  .qr-scanner-stop-button,
  .qr-scanner-switch-camera,
  .qr-scanner-toggle-flash {
    min-width: 160px;
    padding: 16px 28px;
    font-size: 20px;
  }
}
```

### 4. QR Box Optimization
The JavaScript now calculates optimal QR scanning box size for landscape:

```javascript
// Landscape-optimized QR box calculation
if (isLandscape) {
  const maxSize = Math.min(videoHeight * 0.8, videoWidth * 0.6);
  qrboxSize = Math.floor(maxSize);
} else {
  const minDimension = Math.min(videoWidth, videoHeight);
  qrboxSize = Math.floor(minDimension * 0.7);
}

// Ensure reasonable bounds
qrboxSize = Math.max(200, Math.min(qrboxSize, 600));
```

## Webflow-Specific Tips

### 1. Container Setup
- Set the QR scanner container to `width: 100%` and `height: 100vh`
- Remove any max-width constraints in Webflow
- Ensure the container is not inside a constrained parent element

### 2. Button Configuration
- If buttons appear empty, the JavaScript will automatically add text
- You can customize button text in the HTML or let the script handle it
- Ensure buttons have the exact class names specified

### 3. Responsive Settings
- Disable Webflow's default responsive behavior for the scanner container
- The CSS includes specific tablet optimizations that override Webflow defaults
- Test on actual devices for best results

### 4. Performance Tips
- The scanner automatically adjusts FPS and QR box size for optimal performance
- Consider adding loading states for better user experience
- Test camera permissions and provide fallback messaging

## Testing Checklist

- [ ] Camera viewport fills horizontal space in landscape
- [ ] All buttons display text and icons properly
- [ ] QR scanning area is appropriately sized
- [ ] Touch interactions work smoothly on tablets
- [ ] Scanner works on both Surface Go 4 and iPad 11-inch
- [ ] Orientation lock works (if implemented)
- [ ] Performance is smooth during scanning

## Troubleshooting

### Buttons appear empty
- Check that button elements have the correct class names
- The JavaScript will add text automatically if missing
- Verify the script is loading after the HTML elements

### Camera doesn't fill space
- Ensure container has `height: 100vh` and no max-width
- Check that parent elements aren't constraining the size
- Verify the landscape media queries are applying

### Poor scanning performance
- Check console for QR box size calculations
- Ensure adequate lighting for QR code scanning
- Verify the camera has proper focus and isn't obstructed

## Browser Support
- Chrome/Edge: Full support
- Safari: Full support (iOS 11+)
- Firefox: Full support
- WebView: Supported in most tablet apps

For additional support or customization, refer to the complete example in `tablet-optimized-example.html`.
